# version: '3.8'

# for first setup, give admin access to full directory
# takeown /F . /R /D Y; icacls . /grant "$($env:USERDOMAIN)\$($env:USERNAME):(OI)(CI)F" /T

# auth-service start commands
# docker compose down -v && docker system prune -f && docker volume prune -f && docker network prune -f && docker image prune -a -f
# docker compose up -d jaeger prometheus grafana
# docker compose up -d traefik
# docker compose up -d postgres redis rabbitmq mongodb-chat minio
# docker compose up -d audit-logging messaging-service auth-service auth-apigw

# startup cmds
# docker compose build jaeger prometheus grafana
# docker compose up -d jaeger prometheus grafana

# startup traefik
# docker compose build traefik
# docker compose up -d traefik

# Start infrastructure first
# docker compose build postgres redis rabbitmq mongodb-chat minio
# docker compose up -d postgres redis rabbitmq mongodb-chat minio

# Wait for infrastructure to be healthy
# docker compose ps

# Start core services
# docker compose build audit-logging identity-service payment-service
# docker compose up -d audit-logging identity-service payment-service

# Start domain services
# docker compose build auth-service analytics-service agency-service events-meetings-service help-service messaging-service settings-service students-service university-service
# docker compose up -d auth-service analytics-service agency-service events-meetings-service help-service messaging-service settings-service students-service university-service

# Start API gateways
# docker compose build auth-apigw agency-apigw manager-apigw student-apigw super-admin-apigw university-apigw
# docker compose up -d auth-apigw agency-apigw manager-apigw student-apigw super-admin-apigw university-apigw

# Complete cleanup
# docker compose down -v
# docker system prune -f
# docker volume prune -f
# docker network prune -f
# docker image prune -a -f
# docker compose down -v && docker system prune -f && docker volume prune -f && docker network prune -f && docker image prune -a -f

# rebuilding script
# Stop the service
# docker compose stop audit-logging

# Remove the container
# docker compose rm -f audit-logging

# Rebuild the service
# docker compose build audit-logging

# Start the service
# docker compose up -d audit-logging
# Check logs
# docker compose logs -f audit-logging

# Usage:
# Development: docker compose up -d
# Production: docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# grafana dashboard  http://localhost:3000
# prmetheus dashboard http://localhost:9090
# jaeger dashboar http://localhost:16686

networks:
  proxy:
    name: proxy
    driver: bridge
  internal:
    name: internal
    driver: bridge

volumes:
  traefik-logs:
  postgres-data:
  mongodb-chat-data:
  redis-data:
  rabbitmq-data:
  prometheus-data:
  grafana-data:
  minio-data:

services:
  ###################
  # Monitoring Stack
  ###################
  jaeger:
    image: jaegertracing/all-in-one:1.47
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
    ports:
      - '5775:5775/udp'
      - '6831:6831/udp'
      - '6832:6832/udp'
      - '5778:5778'
      - '16686:16686'
      - '14250:14250'
      - '14268:14268'
      - '14269:14269'
      - '9411:9411'
    networks:
      - proxy
    healthcheck:
      test: ['CMD', 'wget', '--spider', 'localhost:16686']
      interval: 5s
      timeout: 3s
      retries: 3

  prometheus:
    image: prom/prometheus:v2.45.0
    volumes:
      - ./monitoring/prometheus:/etc/prometheus
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    ports:
      - '9090:9090'
    networks:
      - proxy
      - internal # Add internal network to ensure Prometheus can reach all services
    healthcheck:
      test: ['CMD', 'wget', '--spider', 'localhost:9090/-/healthy']
      interval: 5s
      timeout: 3s
      retries: 3

  grafana:
    image: grafana/grafana:10.0.3
    volumes:
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - grafana-data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - '3000:3000'
    networks:
      - proxy
    depends_on:
      - prometheus
    healthcheck:
      test: ['CMD', 'wget', '--spider', 'localhost:3000/api/health']
      interval: 5s
      timeout: 3s
      retries: 3

  ###################
  # Reverse Proxy
  ###################
  traefik:
    image: traefik:v2.10
    command:
      # API and Dashboard
      - '--api.insecure=true'
      - '--api.dashboard=true'
      # Docker Provider
      - '--providers.docker=true'
      - '--providers.docker.exposedByDefault=false'
      - '--providers.docker.network=proxy'
      # Entrypoints
      - '--entrypoints.web.address=:80'
      - '--entrypoints.metrics.address=:8082'
      # Prometheus Metrics
      - '--metrics.prometheus=true'
      - '--metrics.prometheus.entrypoint=metrics'
      # Logging
      - '--log.level=INFO'
      - '--log.filePath=/var/log/traefik/traefik.log'
      - '--accesslog=true'
      - '--accesslog.filepath=/var/log/traefik/access.log'
      - '--accesslog.format=json'
      # Tracing
      - '--tracing.jaeger=true'
      - '--tracing.jaeger.samplingServerURL=http://jaeger:16686/sampling'
      - '--tracing.jaeger.samplingType=const'
      - '--tracing.jaeger.samplingParam=1.0'
      - '--tracing.jaeger.localAgentHostPort=jaeger:6831'
    ports:
      - '80:80' # Web
      - '8080:8080' # Dashboard
      - '8082:8082' # Prometheus Metrics
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik-logs:/var/log/traefik
    networks:
      - proxy
    depends_on:
      jaeger:
        condition: service_healthy
      prometheus:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'wget', '--spider', 'http://localhost:8080/api/rawdata']
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    labels:
      - 'traefik.enable=true'
      # Dashboard
      - 'traefik.http.routers.api.rule=Host(`traefik.localhost`)'
      - 'traefik.http.routers.api.service=api@internal'
      - 'traefik.http.routers.api.entrypoints=web'
      # Prometheus Metrics
      - 'traefik.http.routers.metrics.rule=Host(`metrics.localhost`)'
      - 'traefik.http.routers.metrics.service=prometheus@internal'
      - 'traefik.http.routers.metrics.entrypoints=metrics'

  ###################
  # Databases & Infrastructure
  ###################
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - '5432:5432'
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init-scripts/postgres:/docker-entrypoint-initdb.d
    networks:
      - internal
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    ports:
      - '6379:6379'
    networks:
      - internal
    volumes:
      - redis-data:/data
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s

  rabbitmq:
    image: rabbitmq:3-management-alpine
    environment:
      RABBITMQ_DEFAULT_USER: rabbitmq_user
      RABBITMQ_DEFAULT_PASS: rabbitmq_pass
    ports:
      - '5672:5672'
      - '15672:15672'
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq
    networks:
      - proxy
      - internal
    healthcheck:
      test: ['CMD', 'rabbitmq-diagnostics', '-q', 'ping']
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s

  mongodb-chat:
    image: mongo:6-jammy
    environment:
      MONGO_INITDB_ROOT_USERNAME: chat_user
      MONGO_INITDB_ROOT_PASSWORD: chat_pass
      MONGO_INITDB_DATABASE: chat_db
    volumes:
      - mongodb-chat-data:/data/db
    networks:
      - internal
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s

  minio:
    image: minio/minio:latest
    container_name: minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    volumes:
      - minio-data:/data
    ports:
      - '9000:9000' # API port
      - '9001:9001' # Console UI port
    networks:
      - proxy
      - internal
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:9000/minio/health/ready']
      interval: 10s
      timeout: 5s
      retries: 5
    labels:
      - 'traefik.enable=true'
      - 'traefik.http.routers.minio.rule=Host(`minio.localhost`)'
      - 'traefik.http.routers.minio.entrypoints=web'
      - 'traefik.http.services.minio.loadbalancer.server.port=9001'

  ###################
  # Core Services
  ###################
  audit-logging:
    build:
      context: .
      dockerfile: apps/core/audit-logging/Dockerfile.dev
    volumes:
      - .:/app
      - /app/node_modules
      - /app/dist
    ports:
      - '3001:3001'
      - '50051:50051'
    environment:
      - PORT=3001
      - NODE_ENV=development
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=audit_db
      - DB_USER=audit_user
      - DB_PASSWORD=audit_pass
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - NX_CLOUD_ACCESS_TOKEN="" # Add this to disable Nx Cloud
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3001/health']
      interval: 5s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - proxy
      - internal
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=proxy'
      - 'traefik.http.routers.audit-logging.rule=Host(`audit.localhost`)'
      - 'traefik.http.services.audit-logging.loadbalancer.server.port=3001'
      - 'traefik.http.routers.audit-logging.entrypoints=web'
      # Add health check path for Traefik
      - 'traefik.http.services.audit-logging.loadbalancer.healthcheck.path=/health'
      - 'traefik.http.services.audit-logging.loadbalancer.healthcheck.interval=5s'
      - 'traefik.http.services.audit-logging.loadbalancer.healthcheck.timeout=3s'

  identity-service:
    build:
      context: .
      dockerfile: apps/core/identity-service/Dockerfile.dev
    volumes:
      - .:/app
      - /app/node_modules
    ports:
      - '3002:3002'
    environment:
      - PORT=3002
      - NODE_ENV=development
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=identity_db
      - DB_USER=identity_user
      - DB_PASSWORD=identity_pass
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=1
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3002/health']
      interval: 5s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - proxy
      - internal
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=proxy'
      - 'traefik.http.routers.identity.rule=Host(`identity.localhost`)'
      - 'traefik.http.services.identity.loadbalancer.server.port=3002'
      - 'traefik.http.routers.identity.entrypoints=web'
      # Add health check path for Traefik
      - 'traefik.http.services.identity.loadbalancer.healthcheck.path=/health'
      - 'traefik.http.services.identity.loadbalancer.healthcheck.interval=5s'
      - 'traefik.http.services.identity.loadbalancer.healthcheck.timeout=3s'

  payment-service:
    build:
      context: .
      dockerfile: apps/core/payment-service/Dockerfile.dev
    volumes:
      - .:/app
      - /app/node_modules
    ports:
      - '3003:3003'
    environment:
      - PORT=3003
      - NODE_ENV=development
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=payment_db
      - DB_USER=payment_user
      - DB_PASSWORD=payment_pass
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=2
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3003/health']
      interval: 5s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - proxy
      - internal
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=proxy'
      - 'traefik.http.routers.payment.rule=Host(`payment.localhost`)'
      - 'traefik.http.services.payment.loadbalancer.server.port=3003'
      - 'traefik.http.routers.payment.entrypoints=web'
      # Add health check path for Traefik
      - 'traefik.http.services.payment.loadbalancer.healthcheck.path=/health'
      - 'traefik.http.services.payment.loadbalancer.healthcheck.interval=5s'
      - 'traefik.http.services.payment.loadbalancer.healthcheck.timeout=3s'

  ###################
  # API Gateways
  ###################
  agency-apigw:
    build:
      context: .
      dockerfile: apps/gateways/agency-apigw/Dockerfile.dev
    volumes:
      - .:/app
      - /app/node_modules
    ports:
      - '4001:4001'
    environment:
      - PORT=4001
      - NODE_ENV=development
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=3
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4001/health']
      interval: 5s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - proxy
      - internal
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=proxy'
      - 'traefik.http.routers.agency-apigw.rule=Host(`agency-api.localhost`)'
      - 'traefik.http.routers.agency-apigw.entrypoints=web'
      - 'traefik.http.services.agency-apigw.loadbalancer.server.port=3001'
      # Add health check path for Traefik
      - 'traefik.http.services.agency-apigw.loadbalancer.healthcheck.path=/health'
      - 'traefik.http.services.agency-apigw.loadbalancer.healthcheck.interval=5s'
      - 'traefik.http.services.agency-apigw.loadbalancer.healthcheck.timeout=3s'

  manager-apigw:
    build:
      context: .
      dockerfile: apps/gateways/manager-apigw/Dockerfile.dev
    volumes:
      - .:/app
      - /app/node_modules
    ports:
      - '4002:4002'
    environment:
      - PORT=4002
      - NODE_ENV=development
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=4
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4002/health']
      interval: 5s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - proxy
      - internal
    labels:
      - 'traefik.enable=true'
      - 'traefik.http.routers.manager-apigw.rule=Host(`manager-api.localhost`)'
      - 'traefik.http.routers.manager-apigw.entrypoints=web'
      - 'traefik.http.services.manager-apigw.loadbalancer.server.port=4002'
      # Add health check path for Traefik
      - 'traefik.http.services.manager-apigw.loadbalancer.healthcheck.path=/health'
      - 'traefik.http.services.manager-apigw.loadbalancer.healthcheck.interval=5s'
      - 'traefik.http.services.manager-apigw.loadbalancer.healthcheck.timeout=3s'

  student-apigw:
    build:
      context: .
      dockerfile: apps/gateways/student-apigw/Dockerfile.dev
    volumes:
      - .:/app
      - /app/node_modules
    ports:
      - '4003:4003'
    environment:
      - PORT=4003
      - NODE_ENV=development
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=5
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4003/health']
      interval: 5s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - proxy
      - internal
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=proxy'
      - 'traefik.http.routers.student-apigw.rule=Host(`student-api.localhost`)'
      - 'traefik.http.services.student-apigw.loadbalancer.server.port=4003'
      - 'traefik.http.routers.student-apigw.entrypoints=web'
      # Add health check path for Traefik
      - 'traefik.http.services.student-apigw.loadbalancer.healthcheck.path=/health'
      - 'traefik.http.services.student-apigw.loadbalancer.healthcheck.interval=5s'
      - 'traefik.http.services.student-apigw.loadbalancer.healthcheck.timeout=3s'

  super-admin-apigw:
    build:
      context: .
      dockerfile: apps/gateways/super-admin-apigw/Dockerfile.dev
    volumes:
      - .:/app
      - /app/node_modules
    ports:
      - '4004:4004'
    environment:
      - PORT=4004
      - NODE_ENV=development
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=6
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4004/health']
      interval: 5s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - proxy
      - internal
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=proxy'
      - 'traefik.http.routers.super-admin-apigw.rule=Host(`admin-api.localhost`)'
      - 'traefik.http.services.super-admin-apigw.loadbalancer.server.port=4004'
      - 'traefik.http.routers.super-admin-apigw.entrypoints=web'
      # Add health check path for Traefik
      - 'traefik.http.services.super-admin-apigw.loadbalancer.healthcheck.path=/health'
      - 'traefik.http.services.super-admin-apigw.loadbalancer.healthcheck.interval=5s'
      - 'traefik.http.services.super-admin-apigw.loadbalancer.healthcheck.timeout=3s'

  university-apigw:
    build:
      context: .
      dockerfile: apps/gateways/university-apigw/Dockerfile.dev
    volumes:
      - .:/app
      - /app/node_modules
    ports:
      - '4005:4005'
    environment:
      - PORT=4005
      - NODE_ENV=development
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=7
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4005/health']
      interval: 5s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - proxy
      - internal
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=proxy'
      - 'traefik.http.routers.university-apigw.rule=Host(`university-api.localhost`)'
      - 'traefik.http.services.university-apigw.loadbalancer.server.port=4005'
      - 'traefik.http.routers.university-apigw.entrypoints=web'
      # Add health check path for Traefik
      - 'traefik.http.services.university-apigw.loadbalancer.healthcheck.path=/health'
      - 'traefik.http.services.university-apigw.loadbalancer.healthcheck.interval=5s'
      - 'traefik.http.services.university-apigw.loadbalancer.healthcheck.timeout=3s'

  auth-apigw:
    build:
      context: .
      dockerfile: apps/gateways/auth-apigw/Dockerfile.dev
    volumes:
      - .:/app
      - /app/node_modules
      - /app/dist
    ports:
      - '4006:4006'
    environment:
      - PORT=4006
      - NODE_ENV=development
      - JWT_SECRET=applygoal-super-secret-jwt-key-2024
      - METRICS_PORT=4006
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=3
      - HASH_SALT=10
      - S3_ENDPOINT=http://minio:9000
      - S3_PUBLIC_ENDPOINT=http://localhost:9000
      - S3_BUCKET=applygoal-files
      - S3_ACCESS_KEY=minioadmin
      - S3_SECRET_KEY=minioadmin123
      - S3_REGION=us-east-1
      - S3_FORCE_PATH_STYLE=true
    depends_on:
      redis:
        condition: service_healthy
      auth-service:
        condition: service_healthy
      prometheus:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4006/health']
      interval: 5s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - proxy
      - internal
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=proxy'
      - 'traefik.http.routers.auth-apigw.rule=Host(`auth-api.localhost`)'
      - 'traefik.http.routers.auth-apigw.entrypoints=web'
      - 'traefik.http.services.auth-apigw.loadbalancer.server.port=4006'
      # Add health check path for Traefik
      - 'traefik.http.services.auth-apigw.loadbalancer.healthcheck.path=/health'
      - 'traefik.http.services.auth-apigw.loadbalancer.healthcheck.interval=5s'
      - 'traefik.http.services.auth-apigw.loadbalancer.healthcheck.timeout=3s'

  ###################
  # Domain Services
  ###################
  agency-service:
    build:
      context: .
      dockerfile: apps/services/agency-service/Dockerfile.dev
    volumes:
      - .:/app
      - /app/node_modules
    ports:
      - '5001:5001'
    environment:
      - PORT=5001
      - NODE_ENV=development
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=agency_db
      - DB_USER=agency_user
      - DB_PASSWORD=agency_pass
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=8
      - METRICS_PORT=5001 # Add this
    networks:
      - proxy
      - internal
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:5001/health']
      interval: 5s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=proxy'
      - 'traefik.http.routers.agency.rule=Host(`agency.localhost`)'
      - 'traefik.http.services.agency.loadbalancer.server.port=5001'
      - 'traefik.http.routers.agency.entrypoints=web'
      # Add health check path for Traefik
      - 'traefik.http.services.agency.loadbalancer.healthcheck.path=/health'
      - 'traefik.http.services.agency.loadbalancer.healthcheck.interval=5s'
      - 'traefik.http.services.agency.loadbalancer.healthcheck.timeout=3s'
      # Add metrics endpoint configuration
      - 'traefik.http.routers.agency-metrics.rule=Host(`agency.localhost`) && PathPrefix(`/metrics`)'
      - 'traefik.http.services.agency-metrics.loadbalancer.server.port=5001'

  analytics-service:
    build:
      context: .
      dockerfile: apps/services/analytics-service/Dockerfile.dev
    volumes:
      - .:/app
      - /app/node_modules
    ports:
      - '5002:5002'
    environment:
      - PORT=5002
      - NODE_ENV=development
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=analytics_db
      - DB_USER=analytics_user
      - DB_PASSWORD=analytics_pass
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=9
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:5002/health']
      interval: 5s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=proxy'
      - 'traefik.http.routers.analytics.rule=Host(`analytics.localhost`)'
      - 'traefik.http.services.analytics.loadbalancer.server.port=5002'
      - 'traefik.http.routers.analytics.entrypoints=web'
      # Add health check path for Traefik
      - 'traefik.http.services.analytics.loadbalancer.healthcheck.path=/health'
      - 'traefik.http.services.analytics.loadbalancer.healthcheck.interval=5s'
      - 'traefik.http.services.analytics.loadbalancer.healthcheck.timeout=3s'
    networks:
      - proxy
      - internal

  auth-service:
    build:
      context: .
      dockerfile: apps/services/auth-service/Dockerfile.dev
    volumes:
      - .:/app
      - /app/node_modules
    ports:
      - '5003:5003'
      - '50052:50052'
    environment:
      - PORT=5003
      - NODE_ENV=development
      - JWT_SECRET=applygoal-super-secret-jwt-key-2024
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=auth_db
      - DB_USER=auth_user
      - DB_PASSWORD=auth_pass
      - ACCESS_TOKEN_EXPIRY='1h'
      - REFRESH_TOKEN_EXPIRY='7d'
      - REFRESH_EXPIRY_MS=604800000
      - ACCESS_EXPIRY_MS=3600000
      - RABBITMQ_EXCHANGE=messaging_exchange
      - NODE_ENV=development
      - RABBITMQ_QUEUE=messaging_emails_queue
      - RABBITMQ_USER=rabbitmq_user
      - RABBITMQ_PASS=rabbitmq_pass
      - RABBITMQ_URI=amqp://rabbitmq_user:rabbitmq_pass@rabbitmq:5672
      - GOOGLE_CLIENT_ID=1008827733358-nok1v02dqkfgkh54pb0337fitvdktjf3.apps.googleusercontent.com
      - STORAGE_DRIVER=s3
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      minio:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:5003/health']
      interval: 5s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=proxy'
      - 'traefik.http.routers.auth.rule=Host(`auth.localhost`)'
      - 'traefik.http.services.auth.loadbalancer.server.port=5003'
      - 'traefik.http.routers.auth.entrypoints=web'
      # Add health check path for Traefik
      - 'traefik.http.services.auth.loadbalancer.healthcheck.path=/health'
      - 'traefik.http.services.auth.loadbalancer.healthcheck.interval=5s'
      - 'traefik.http.services.auth.loadbalancer.healthcheck.timeout=3s'
    networks:
      - proxy
      - internal

  events-meetings-service:
    build:
      context: .
      dockerfile: apps/services/events-meetings-service/Dockerfile.dev
    volumes:
      - .:/app
      - /app/node_modules
    ports:
      - '5004:5004'
    environment:
      - PORT=5004
      - NODE_ENV=development
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=events_db
      - DB_USER=events_user
      - DB_PASSWORD=events_pass
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:5004/health']
      interval: 5s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=proxy'
      - 'traefik.http.routers.events-meetings.rule=Host(`events-meetings.localhost`)'
      - 'traefik.http.services.events-meetings.loadbalancer.server.port=5004'
      - 'traefik.http.routers.events-meetings.entrypoints=web'
      # Add health check path for Traefik
      - 'traefik.http.services.events-meetings.loadbalancer.healthcheck.path=/health'
      - 'traefik.http.services.events-meetings.loadbalancer.healthcheck.interval=5s'
      - 'traefik.http.services.events-meetings.loadbalancer.healthcheck.timeout=3s'
    networks:
      - proxy
      - internal

  help-service:
    build:
      context: .
      dockerfile: apps/services/help-service/Dockerfile.dev
    volumes:
      - .:/app
      - /app/node_modules
    ports:
      - '5005:5005'
    environment:
      - PORT=5005
      - NODE_ENV=development
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=help_db
      - DB_USER=help_user
      - DB_PASSWORD=help_pass
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:5005/health']
      interval: 5s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=proxy'
      - 'traefik.http.routers.help.rule=Host(`help.localhost`)'
      - 'traefik.http.services.help.loadbalancer.server.port=5005'
      - 'traefik.http.routers.help.entrypoints=web'
      # Add health check path for Traefik
      - 'traefik.http.services.help.loadbalancer.healthcheck.path=/health'
      - 'traefik.http.services.help.loadbalancer.healthcheck.interval=5s'
      - 'traefik.http.services.help.loadbalancer.healthcheck.timeout=3s'
    networks:
      - proxy
      - internal

  messaging-service:
    build:
      context: .
      dockerfile: apps/services/messaging-service/Dockerfile.dev
    volumes:
      - .:/app
      - /app/node_modules
    ports:
      - '5006:5006'
    environment:
      - PORT=5006
      - NODE_ENV=development
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=10
      - METRICS_PORT=5006
      - RABBITMQ_URL=amqp://rabbitmq:5672
      - RABBITMQ_USER=rabbitmq_user
      - RABBITMQ_PASS=rabbitmq_pass
      - GMAIL_CLIENT_ID=880130374571-e07gklv7c90no5bovnv52bjmak27hmm4.apps.googleusercontent.com
      - GMAIL_CLIENT_SECRET=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
      - GMAIL_REFRESH_TOKEN=1//047qJM9vn2YHfCgYIARAAGAQSNwF-L9IrfF50hMX6lBbe51SVJ57UTK9bh6XZJrJoJPOW7TOkjlolbcrhs7N7eLBl28q8kuS6Dcs
      - GMAIL_SENDER=<EMAIL>
      - MONGO_URI=*************************************************************************
    depends_on:
      mongodb-chat:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:5006/health']
      interval: 5s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - proxy
      - internal
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=proxy'
      - 'traefik.http.routers.messaging.rule=Host(`messaging.localhost`)'
      - 'traefik.http.services.messaging.loadbalancer.server.port=5006'
      - 'traefik.http.routers.messaging.entrypoints=web'
      # Add health check path for Traefik
      - 'traefik.http.services.messaging.loadbalancer.healthcheck.path=/health'
      - 'traefik.http.services.messaging.loadbalancer.healthcheck.interval=5s'
      - 'traefik.http.services.messaging.loadbalancer.healthcheck.timeout=3s'
      # Add metrics endpoint configuration
      - 'traefik.http.routers.messaging-metrics.rule=Host(`messaging.localhost`) && PathPrefix(`/metrics`)'
      - 'traefik.http.services.messaging-metrics.loadbalancer.server.port=5006'

  settings-service:
    build:
      context: .
      dockerfile: apps/services/settings-service/Dockerfile.dev
    volumes:
      - .:/app
      - /app/node_modules
    ports:
      - '5007:5007'
    environment:
      - PORT=5007
      - NODE_ENV=development
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=settings_db
      - DB_USER=settings_user
      - DB_PASSWORD=settings_pass
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=11
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:5007/health']
      interval: 5s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=proxy'
      - 'traefik.http.routers.settings.rule=Host(`settings.localhost`)'
      - 'traefik.http.services.settings.loadbalancer.server.port=5007'
      - 'traefik.http.routers.settings.entrypoints=web'
      # Add health check path for Traefik
      - 'traefik.http.services.settings.loadbalancer.healthcheck.path=/health'
      - 'traefik.http.services.settings.loadbalancer.healthcheck.interval=5s'
      - 'traefik.http.services.settings.loadbalancer.healthcheck.timeout=3s'
    networks:
      - proxy
      - internal

  students-service:
    build:
      context: .
      dockerfile: apps/services/students-service/Dockerfile.dev
    volumes:
      - .:/app
      - /app/node_modules
    ports:
      - '5008:5008'
    environment:
      - PORT=5008
      - NODE_ENV=development
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=students_db
      - DB_USER=students_user
      - DB_PASSWORD=students_pass
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:5008/health']
      interval: 5s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=proxy'
      - 'traefik.http.routers.students.rule=Host(`students.localhost`)'
      - 'traefik.http.services.students.loadbalancer.server.port=5008'
      - 'traefik.http.routers.students.entrypoints=web'
      # Add health check path for Traefik
      - 'traefik.http.services.students.loadbalancer.healthcheck.path=/health'
      - 'traefik.http.services.students.loadbalancer.healthcheck.interval=5s'
      - 'traefik.http.services.students.loadbalancer.healthcheck.timeout=3s'
    networks:
      - proxy
      - internal

  university-service:
    build:
      context: .
      dockerfile: apps/services/university-service/Dockerfile.dev
    volumes:
      - .:/app
      - /app/node_modules
    ports:
      - '5009:5009'
    environment:
      - PORT=5009
      - NODE_ENV=development
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=university_db
      - DB_USER=university_user
      - DB_PASSWORD=university_pass
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:5009/health']
      interval: 5s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=proxy'
      - 'traefik.http.routers.university.rule=Host(`university.localhost`)'
      - 'traefik.http.services.university.loadbalancer.server.port=5009'
      - 'traefik.http.routers.university.entrypoints=web'
      # Add health check path for Traefik
      - 'traefik.http.services.university.loadbalancer.healthcheck.path=/health'
      - 'traefik.http.services.university.loadbalancer.healthcheck.interval=5s'
      - 'traefik.http.services.university.loadbalancer.healthcheck.timeout=3s'
    networks:
      - proxy
      - internal
