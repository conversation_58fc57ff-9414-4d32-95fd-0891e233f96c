# Organization-specific Role Management Implementation

## Overview
Updated the role system to support organization-specific roles, allowing different organizations to have their own custom roles while maintaining system-wide roles for global functionality.

## Key Features Implemented

### 1. **Multi-tenant Role Support**
- ✅ **System Roles**: Global roles like "Super Admin" that work across all organizations
- ✅ **Organization Roles**: Custom roles specific to each organization
- ✅ **Role Inheritance**: Organizations can use system roles or create their own
- ✅ **Role Isolation**: Organization roles don't conflict with each other

### 2. **Database Schema Updates**

#### **Role Model Enhancements**
**File**: `apps/services/auth-service/src/app/user/model/role.model.ts`

**New Fields Added**:
- `organizationId?: number` - Links role to specific organization (null for system roles)
- `description?: string` - Role description for better management
- `isSystemRole: boolean` - Identifies global vs organization-specific roles

**New Relationships**:
- `BelongsTo Organization` - Role belongs to an organization
- `Organization HasMany Roles` - Organization can have multiple roles

#### **Database Migration**
**File**: `apps/services/auth-service/src/app/migration/migrations/20250629-add-organization-to-roles.js`

**Changes Applied**:
- Added `organizationId` column with foreign key to organizations table
- Added `description` column for role descriptions
- Added `isSystemRole` boolean column with default false
- Added indexes for performance optimization
- Updated existing roles to be system roles for backward compatibility

### 3. **Role Management Service**
**File**: `apps/services/auth-service/src/app/role/role.service.ts`

**Features**:
- ✅ **Create organization-specific roles**
- ✅ **List roles by organization**
- ✅ **Update role properties and permissions**
- ✅ **Delete organization roles** (system roles protected)
- ✅ **Get roles for organization** (includes system roles)
- ✅ **Permission management** for roles

**Key Methods**:
- `createRole()` - Create organization or system roles
- `listRoles()` - List roles with filtering options
- `getRolesForOrganization()` - Get all available roles for an organization
- `updateRole()` - Update role properties
- `deleteRole()` - Delete non-system roles

### 4. **Role Management Controller**
**File**: `apps/services/auth-service/src/app/role/role.controller.ts`

**gRPC Methods**:
- `CreateRole` - Create new roles
- `ListRoles` - List roles with filters
- `GetRole` - Get role by ID
- `UpdateRole` - Update role properties
- `DeleteRole` - Delete role
- `GetOrganizationRoles` - Get roles for specific organization

### 5. **API Endpoints**
**File**: `apps/gateways/auth-apigw/src/app/auth.controller.ts`

**New Endpoints**:
- `POST /organizations/:orgId/roles` - Create organization-specific role
- `GET /organizations/:orgId/roles` - Get roles for organization
- `GET /roles` - List all roles with filters
- `GET /roles/:id` - Get specific role
- `PUT /roles/:id` - Update role
- `DELETE /roles/:id` - Delete role

### 6. **Enhanced Employee Creation**
**File**: `apps/services/auth-service/src/app/employee/employee.service.ts`

**Role Resolution Logic**:
1. **Try organization-specific role** first
2. **Fallback to system role** if not found
3. **Auto-create organization role** if neither exists
4. **Proper error handling** for invalid roles

### 7. **Updated Seed Data**
**File**: `apps/services/auth-service/src/app/seed/seed.service.ts`

**Seeding Strategy**:
- ✅ **System roles** marked with `isSystemRole: true`
- ✅ **Default organization roles** created for ApplyGoal
- ✅ **Backward compatibility** maintained
- ✅ **Role descriptions** added for clarity

## API Usage Examples

### Create Organization Role
```bash
curl -X POST "http://localhost:4006/api/auth/organizations/1/roles" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Department Manager",
    "description": "Manager role for specific department",
    "permissions": [
      {"featureId": 1, "hasPermission": true},
      {"featureId": 2, "hasPermission": true}
    ]
  }'
```

### List Organization Roles
```bash
curl -X GET "http://localhost:4006/api/auth/organizations/1/roles" \
  -H "Authorization: Bearer $TOKEN"
```

### List All Roles with Filters
```bash
curl -X GET "http://localhost:4006/api/auth/roles?organizationId=1&includeSystemRoles=true&page=1&limit=10" \
  -H "Authorization: Bearer $TOKEN"
```

### Create Employee with Organization Role
```bash
curl -X POST "http://localhost:4006/api/auth/employees" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "employeeRoleName": "Department Manager",
    "organizationName": "ApplyGoal",
    "firstName": "John",
    "lastName": "Manager"
  }'
```

## Role Resolution Flow

### For Employee Creation:
1. **Check organization-specific role**: `name = "Manager" AND organizationId = 1`
2. **Check system role**: `name = "Manager" AND isSystemRole = true`
3. **Auto-create if needed**: Create organization-specific role
4. **Assign to user**: Link user to resolved role

### For Role Listing:
1. **Organization roles**: `organizationId = X AND isSystemRole = false`
2. **System roles**: `organizationId IS NULL AND isSystemRole = true`
3. **Combined view**: Show both types with clear identification

## Benefits

### 1. **Multi-tenant Support**
- Different organizations can have different role structures
- No role name conflicts between organizations
- Flexible role management per organization

### 2. **Backward Compatibility**
- Existing system roles continue to work
- Gradual migration to organization-specific roles
- No breaking changes to existing functionality

### 3. **Scalability**
- Each organization manages its own roles
- System roles provide global functionality
- Easy to add new organizations with custom roles

### 4. **Security**
- Role isolation between organizations
- System roles protected from deletion
- Proper permission inheritance

### 5. **Flexibility**
- Organizations can create custom roles
- Inherit from system roles when needed
- Dynamic role creation during employee setup

## Testing

### Automated Testing
Run: `./test-organization-roles.sh`

### Manual Testing Steps
1. **Run migration** to add organization support to roles
2. **Re-seed database** with updated role structure
3. **Create organization role** using API
4. **List organization roles** to verify creation
5. **Create employee** with organization-specific role
6. **Verify role assignment** in user management

## Migration Notes

### Database Changes Required
1. **Run migration**: Add organizationId, description, isSystemRole columns
2. **Update indexes**: Add performance indexes for role queries
3. **Seed update**: Mark existing roles as system roles

### Deployment Steps
1. **Stop auth-service**
2. **Run database migration**
3. **Update seed data**
4. **Restart services**
5. **Verify role functionality**

## Future Enhancements

### Potential Improvements
- **Role templates** for quick organization setup
- **Role inheritance** from parent organizations
- **Role approval workflow** for organization admins
- **Role usage analytics** and reporting
- **Bulk role operations** for large organizations

The organization-specific role system provides a robust foundation for multi-tenant role management while maintaining backward compatibility and system integrity.
