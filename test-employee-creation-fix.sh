#!/bin/bash

echo "🔧 Testing Employee Creation Fix..."

echo "1. Restarting auth-service to apply the fix..."
docker compose stop auth-service
docker compose rm -f auth-service
docker compose up -d auth-service

echo "2. Waiting for auth-service to be healthy..."
sleep 15

for i in {1..10}; do
    if curl -f -s http://localhost:5003/health > /dev/null; then
        echo "✅ Auth service is healthy"
        break
    else
        echo "⏳ Waiting for auth-service... (attempt $i/10)"
        sleep 3
    fi
done

echo "3. Getting fresh Super Admin token..."
TOKEN=$(curl -s -X POST "http://localhost:4006/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SuperAdmin@123",
    "ipAddress": "127.0.0.1"
  }' | jq -r '.accessToken')

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
    echo "❌ Failed to get token. Check login credentials."
    exit 1
fi

echo "✅ Got fresh token: ${TOKEN:0:50}..."

echo ""
echo "4. Testing Employee Creation (the main issue)..."

# Test with a unique userId to avoid conflicts
UNIQUE_USER_ID=$((1000 + RANDOM % 9000))

CREATE_RESPONSE=$(curl -s -w "%{http_code}" -X POST "http://localhost:4006/api/auth/employees" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -H "User-Agent: curl/7.68.0" \
  -d "{
    \"userId\": $UNIQUE_USER_ID,
    \"firstName\": \"Test\",
    \"lastName\": \"Employee\",
    \"jobType\": \"Full-time\",
    \"jobStatus\": \"Active\",
    \"dateOfBirth\": \"1990-01-01\",
    \"bloodGroup\": \"O+\",
    \"gender\": \"Male\",
    \"addresses\": [
      {
        \"addressType\": \"Home\",
        \"addressLine\": \"123 Test Street\",
        \"country\": \"Canada\",
        \"state\": \"Ontario\",
        \"city\": \"Toronto\",
        \"postalCode\": \"M5V 3A8\"
      }
    ],
    \"emergencyContacts\": [
      {
        \"contactType\": \"Emergency\",
        \"contactName\": \"Test Contact\",
        \"contactPhone\": \"******-0123\",
        \"contactEmail\": \"<EMAIL>\",
        \"relationship\": \"Friend\"
      }
    ]
  }")

CREATE_HTTP_CODE="${CREATE_RESPONSE: -3}"
CREATE_BODY="${CREATE_RESPONSE%???}"

echo "HTTP Status: $CREATE_HTTP_CODE"

if [ "$CREATE_HTTP_CODE" = "200" ] || [ "$CREATE_HTTP_CODE" = "201" ]; then
    echo "✅ Employee Creation is now working!"
    echo "Response:"
    echo "$CREATE_BODY" | jq . 2>/dev/null || echo "$CREATE_BODY"
    
    echo ""
    echo "5. Testing Employee Listing..."
    LIST_RESPONSE=$(curl -s -w "%{http_code}" -X GET "http://localhost:4006/api/auth/employees" \
      -H "Authorization: Bearer $TOKEN" \
      -H "User-Agent: curl/7.68.0")

    LIST_HTTP_CODE="${LIST_RESPONSE: -3}"
    LIST_BODY="${LIST_RESPONSE%???}"

    if [ "$LIST_HTTP_CODE" = "200" ]; then
        echo "✅ Employee Listing is working!"
        echo "Total employees found: $(echo "$LIST_BODY" | jq -r '.total' 2>/dev/null || echo "N/A")"
    else
        echo "❌ Employee Listing failed. HTTP $LIST_HTTP_CODE"
        echo "$LIST_BODY"
    fi
    
    echo ""
    echo "6. Testing Get Employee by ID..."
    GET_RESPONSE=$(curl -s -w "%{http_code}" -X GET "http://localhost:4006/api/auth/employees/$UNIQUE_USER_ID" \
      -H "Authorization: Bearer $TOKEN" \
      -H "User-Agent: curl/7.68.0")

    GET_HTTP_CODE="${GET_RESPONSE: -3}"
    GET_BODY="${GET_RESPONSE%???}"

    if [ "$GET_HTTP_CODE" = "200" ]; then
        echo "✅ Get Employee by ID is working!"
        echo "Employee name: $(echo "$GET_BODY" | jq -r '.employee.firstName + " " + .employee.lastName' 2>/dev/null || echo "N/A")"
    else
        echo "❌ Get Employee by ID failed. HTTP $GET_HTTP_CODE"
        echo "$GET_BODY"
    fi

else
    echo "❌ Employee Creation still failing. HTTP $CREATE_HTTP_CODE"
    echo "Response: $CREATE_BODY"
    
    echo ""
    echo "🔍 Checking auth-service logs for errors..."
    docker compose logs --tail=20 auth-service | grep -i "error\|exception\|employee"
    
    echo ""
    echo "💡 Possible issues to check:"
    echo "1. Database connection issues"
    echo "2. Missing user with ID $UNIQUE_USER_ID"
    echo "3. Validation errors in employee data"
    echo "4. Database constraints or foreign key issues"
fi

echo ""
echo "📋 Summary:"
echo "Employee Creation: $([ "$CREATE_HTTP_CODE" = "200" ] || [ "$CREATE_HTTP_CODE" = "201" ] && echo "✅ WORKING" || echo "❌ FAILED ($CREATE_HTTP_CODE)")"

echo ""
echo "🎯 Your fresh token for manual testing:"
echo "export TOKEN='$TOKEN'"
echo ""
echo "Manual test commands:"
echo "# Create employee"
echo "curl -X POST 'http://localhost:4006/api/auth/employees' \\"
echo "  -H 'Authorization: Bearer \$TOKEN' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"userId\": 1234, \"firstName\": \"Manual\", \"lastName\": \"Test\", \"jobType\": \"Full-time\", \"jobStatus\": \"Active\"}'"
echo ""
echo "# List employees"
echo "curl -X GET 'http://localhost:4006/api/auth/employees' -H 'Authorization: Bearer \$TOKEN'"

echo ""
echo "🔍 Debug commands if issues persist:"
echo "docker compose logs -f auth-service | grep -i employee"
echo "docker compose logs -f auth-apigw | grep -i employee"
