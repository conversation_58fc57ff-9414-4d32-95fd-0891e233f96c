#!/bin/bash

echo "🔧 Testing Organization-specific Role Management..."

echo "1. Running database migration for organization roles..."
# Note: In a real scenario, you'd run the migration here
# docker compose exec auth-service npm run migrate

echo "2. Restarting auth-service to apply role model changes..."
docker compose stop auth-service
docker compose rm -f auth-service
docker compose up -d auth-service

echo "3. Waiting for auth-service to be healthy..."
sleep 15

for i in {1..10}; do
    if curl -f -s http://localhost:5003/health > /dev/null; then
        echo "✅ Auth service is healthy"
        break
    else
        echo "⏳ Waiting for auth-service... (attempt $i/10)"
        sleep 3
    fi
done

echo "4. Re-seeding database with organization-specific roles..."
docker compose exec auth-service npm run seed

echo "5. Getting Super Admin token..."
TOKEN=$(curl -s -X POST "http://localhost:4006/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SuperAdmin@123",
    "ipAddress": "127.0.0.1"
  }' | jq -r '.accessToken')

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
    echo "❌ Failed to get token. Check login credentials."
    exit 1
fi

echo "✅ Got Super Admin token: ${TOKEN:0:50}..."

echo ""
echo "6. Testing Organization Role Management..."

# First, let's get the ApplyGoal organization ID
echo "Getting ApplyGoal organization ID..."
ORG_RESPONSE=$(curl -s -X GET "http://localhost:4006/api/auth/organizations" \
  -H "Authorization: Bearer $TOKEN")

ORG_ID=$(echo "$ORG_RESPONSE" | jq -r '.organizations[] | select(.name == "ApplyGoal") | .id' 2>/dev/null)

if [ "$ORG_ID" = "null" ] || [ -z "$ORG_ID" ]; then
    echo "❌ Could not find ApplyGoal organization ID"
    echo "Organization response: $ORG_RESPONSE"
    exit 1
fi

echo "✅ Found ApplyGoal organization ID: $ORG_ID"

echo ""
echo "7. Testing Create Organization-specific Role..."
CREATE_ROLE_RESPONSE=$(curl -s -w "%{http_code}" -X POST "http://localhost:4006/api/auth/organizations/$ORG_ID/roles" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Manager",
    "description": "Test manager role for ApplyGoal organization",
    "permissions": []
  }')

CREATE_ROLE_HTTP_CODE="${CREATE_ROLE_RESPONSE: -3}"
CREATE_ROLE_BODY="${CREATE_ROLE_RESPONSE%???}"

echo "HTTP Status: $CREATE_ROLE_HTTP_CODE"

if [ "$CREATE_ROLE_HTTP_CODE" = "200" ] || [ "$CREATE_ROLE_HTTP_CODE" = "201" ]; then
    echo "✅ Organization role creation is working!"
    echo "Response: $CREATE_ROLE_BODY" | jq . 2>/dev/null || echo "$CREATE_ROLE_BODY"
else
    echo "❌ Organization role creation failed. HTTP $CREATE_ROLE_HTTP_CODE"
    echo "Response: $CREATE_ROLE_BODY"
fi

echo ""
echo "8. Testing Get Organization Roles..."
GET_ORG_ROLES_RESPONSE=$(curl -s -w "%{http_code}" -X GET "http://localhost:4006/api/auth/organizations/$ORG_ID/roles" \
  -H "Authorization: Bearer $TOKEN")

GET_ORG_ROLES_HTTP_CODE="${GET_ORG_ROLES_RESPONSE: -3}"
GET_ORG_ROLES_BODY="${GET_ORG_ROLES_RESPONSE%???}"

echo "HTTP Status: $GET_ORG_ROLES_HTTP_CODE"

if [ "$GET_ORG_ROLES_HTTP_CODE" = "200" ]; then
    echo "✅ Get organization roles is working!"
    echo "Organization roles:"
    echo "$GET_ORG_ROLES_BODY" | jq '.roles[] | {id, name, description, isSystemRole}' 2>/dev/null || echo "$GET_ORG_ROLES_BODY"
else
    echo "❌ Get organization roles failed. HTTP $GET_ORG_ROLES_HTTP_CODE"
    echo "Response: $GET_ORG_ROLES_BODY"
fi

echo ""
echo "9. Testing List All Roles (including system roles)..."
LIST_ROLES_RESPONSE=$(curl -s -w "%{http_code}" -X GET "http://localhost:4006/api/auth/roles?includeSystemRoles=true&limit=20" \
  -H "Authorization: Bearer $TOKEN")

LIST_ROLES_HTTP_CODE="${LIST_ROLES_RESPONSE: -3}"
LIST_ROLES_BODY="${LIST_ROLES_RESPONSE%???}"

echo "HTTP Status: $LIST_ROLES_HTTP_CODE"

if [ "$LIST_ROLES_HTTP_CODE" = "200" ]; then
    echo "✅ List all roles is working!"
    echo "Total roles found: $(echo "$LIST_ROLES_BODY" | jq -r '.total' 2>/dev/null || echo "N/A")"
    echo "System roles:"
    echo "$LIST_ROLES_BODY" | jq '.roles[] | select(.isSystemRole == true) | {id, name, isSystemRole}' 2>/dev/null || echo "Could not parse system roles"
    echo "Organization roles:"
    echo "$LIST_ROLES_BODY" | jq '.roles[] | select(.isSystemRole == false) | {id, name, organizationId, isSystemRole}' 2>/dev/null || echo "Could not parse org roles"
else
    echo "❌ List all roles failed. HTTP $LIST_ROLES_HTTP_CODE"
    echo "Response: $LIST_ROLES_BODY"
fi

echo ""
echo "10. Testing Employee Creation with Organization-specific Role..."
UNIQUE_EMAIL="orgrole$(date +%s)@applygoal.com"

CREATE_EMPLOYEE_RESPONSE=$(curl -s -w "%{http_code}" -X POST "http://localhost:4006/api/auth/employees" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"email\": \"$UNIQUE_EMAIL\",
    \"password\": \"Employee123!\",
    \"firstName\": \"Org\",
    \"lastName\": \"Employee\",
    \"employeeRoleName\": \"Test Manager\",
    \"organizationName\": \"ApplyGoal\",
    \"jobType\": \"Full-time\",
    \"jobStatus\": \"Active\"
  }")

CREATE_EMPLOYEE_HTTP_CODE="${CREATE_EMPLOYEE_RESPONSE: -3}"
CREATE_EMPLOYEE_BODY="${CREATE_EMPLOYEE_RESPONSE%???}"

echo "HTTP Status: $CREATE_EMPLOYEE_HTTP_CODE"

if [ "$CREATE_EMPLOYEE_HTTP_CODE" = "200" ] || [ "$CREATE_EMPLOYEE_HTTP_CODE" = "201" ]; then
    echo "✅ Employee creation with organization role is working!"
    echo "Employee created with role: Test Manager"
else
    echo "❌ Employee creation with organization role failed. HTTP $CREATE_EMPLOYEE_HTTP_CODE"
    echo "Response: $CREATE_EMPLOYEE_BODY"
fi

echo ""
echo "📋 Summary:"
echo "Organization Role Creation: $([ "$CREATE_ROLE_HTTP_CODE" = "200" ] || [ "$CREATE_ROLE_HTTP_CODE" = "201" ] && echo "✅ WORKING" || echo "❌ FAILED ($CREATE_ROLE_HTTP_CODE)")"
echo "Get Organization Roles: $([ "$GET_ORG_ROLES_HTTP_CODE" = "200" ] && echo "✅ WORKING" || echo "❌ FAILED ($GET_ORG_ROLES_HTTP_CODE)")"
echo "List All Roles: $([ "$LIST_ROLES_HTTP_CODE" = "200" ] && echo "✅ WORKING" || echo "❌ FAILED ($LIST_ROLES_HTTP_CODE)")"
echo "Employee with Org Role: $([ "$CREATE_EMPLOYEE_HTTP_CODE" = "200" ] || [ "$CREATE_EMPLOYEE_HTTP_CODE" = "201" ] && echo "✅ WORKING" || echo "❌ FAILED ($CREATE_EMPLOYEE_HTTP_CODE)")"

echo ""
echo "🎯 Test Data Used:"
echo "Organization ID: $ORG_ID"
echo "Test Role: Test Manager"
echo "Employee Email: $UNIQUE_EMAIL"

echo ""
echo "🔍 Debug commands if issues persist:"
echo "docker compose logs -f auth-service | grep -i role"
echo "docker compose logs -f auth-apigw | grep -i role"

echo ""
echo "Manual test commands:"
echo "# List organization roles"
echo "curl -X GET 'http://localhost:4006/api/auth/organizations/$ORG_ID/roles' -H 'Authorization: Bearer \$TOKEN'"
echo ""
echo "# Create organization role"
echo "curl -X POST 'http://localhost:4006/api/auth/organizations/$ORG_ID/roles' \\"
echo "  -H 'Authorization: Bearer \$TOKEN' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"name\": \"Custom Role\", \"description\": \"Custom role for testing\"}'"
