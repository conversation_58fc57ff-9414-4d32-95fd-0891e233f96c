#!/bin/bash

echo "🔧 Testing Organization-specific Department Management..."

echo "1. Restarting auth-service to apply department model changes..."
docker compose stop auth-service
docker compose rm -f auth-service
docker compose up -d auth-service

echo "2. Waiting for auth-service to be healthy..."
sleep 15

for i in {1..10}; do
    if curl -f -s http://localhost:5003/health > /dev/null; then
        echo "✅ Auth service is healthy"
        break
    else
        echo "⏳ Waiting for auth-service... (attempt $i/10)"
        sleep 3
    fi
done

echo "3. Getting Super Admin token..."
TOKEN=$(curl -s -X POST "http://localhost:4006/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SuperAdmin@123",
    "ipAddress": "127.0.0.1"
  }' | jq -r '.accessToken')

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
    echo "❌ Failed to get token. Check login credentials."
    exit 1
fi

echo "✅ Got Super Admin token: ${TOKEN:0:50}..."

echo ""
echo "4. Getting ApplyGoal organization ID..."
ORG_RESPONSE=$(curl -s -X GET "http://localhost:4006/api/auth/organizations" \
  -H "Authorization: Bearer $TOKEN")

ORG_ID=$(echo "$ORG_RESPONSE" | jq -r '.organizations[] | select(.name == "ApplyGoal") | .id' 2>/dev/null)

if [ "$ORG_ID" = "null" ] || [ -z "$ORG_ID" ]; then
    echo "❌ Could not find ApplyGoal organization ID"
    echo "Organization response: $ORG_RESPONSE"
    exit 1
fi

echo "✅ Found ApplyGoal organization ID: $ORG_ID"

echo ""
echo "5. Testing Create Organization Department..."
CREATE_DEPT_RESPONSE=$(curl -s -w "%{http_code}" -X POST "http://localhost:4006/api/auth/organizations/$ORG_ID/departments" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Engineering",
    "description": "Test engineering department for ApplyGoal"
  }')

CREATE_DEPT_HTTP_CODE="${CREATE_DEPT_RESPONSE: -3}"
CREATE_DEPT_BODY="${CREATE_DEPT_RESPONSE%???}"

echo "HTTP Status: $CREATE_DEPT_HTTP_CODE"

if [ "$CREATE_DEPT_HTTP_CODE" = "200" ] || [ "$CREATE_DEPT_HTTP_CODE" = "201" ]; then
    echo "✅ Organization department creation is working!"
    echo "Response: $CREATE_DEPT_BODY" | jq . 2>/dev/null || echo "$CREATE_DEPT_BODY"
    
    # Extract department ID for further testing
    DEPT_ID=$(echo "$CREATE_DEPT_BODY" | jq -r '.department.id' 2>/dev/null)
else
    echo "❌ Organization department creation failed. HTTP $CREATE_DEPT_HTTP_CODE"
    echo "Response: $CREATE_DEPT_BODY"
fi

echo ""
echo "6. Testing Get Organization Departments..."
GET_ORG_DEPTS_RESPONSE=$(curl -s -w "%{http_code}" -X GET "http://localhost:4006/api/auth/organizations/$ORG_ID/departments" \
  -H "Authorization: Bearer $TOKEN")

GET_ORG_DEPTS_HTTP_CODE="${GET_ORG_DEPTS_RESPONSE: -3}"
GET_ORG_DEPTS_BODY="${GET_ORG_DEPTS_RESPONSE%???}"

echo "HTTP Status: $GET_ORG_DEPTS_HTTP_CODE"

if [ "$GET_ORG_DEPTS_HTTP_CODE" = "200" ]; then
    echo "✅ Get organization departments is working!"
    echo "Organization departments:"
    echo "$GET_ORG_DEPTS_BODY" | jq '.departments[] | {id, name, organizationId}' 2>/dev/null || echo "$GET_ORG_DEPTS_BODY"
else
    echo "❌ Get organization departments failed. HTTP $GET_ORG_DEPTS_HTTP_CODE"
    echo "Response: $GET_ORG_DEPTS_BODY"
fi

echo ""
echo "7. Testing List Organization Departments with Pagination..."
LIST_DEPTS_RESPONSE=$(curl -s -w "%{http_code}" -X GET "http://localhost:4006/api/auth/org-departments?organizationId=$ORG_ID&limit=20" \
  -H "Authorization: Bearer $TOKEN")

LIST_DEPTS_HTTP_CODE="${LIST_DEPTS_RESPONSE: -3}"
LIST_DEPTS_BODY="${LIST_DEPTS_RESPONSE%???}"

echo "HTTP Status: $LIST_DEPTS_HTTP_CODE"

if [ "$LIST_DEPTS_HTTP_CODE" = "200" ]; then
    echo "✅ List organization departments is working!"
    echo "Total departments found: $(echo "$LIST_DEPTS_BODY" | jq -r '.total' 2>/dev/null || echo "N/A")"
    echo "Departments:"
    echo "$LIST_DEPTS_BODY" | jq '.departments[] | {id, name, organizationId}' 2>/dev/null || echo "Could not parse departments"
else
    echo "❌ List organization departments failed. HTTP $LIST_DEPTS_HTTP_CODE"
    echo "Response: $LIST_DEPTS_BODY"
fi

if [ "$DEPT_ID" != "null" ] && [ -n "$DEPT_ID" ]; then
    echo ""
    echo "8. Testing Get Department by ID..."
    GET_DEPT_RESPONSE=$(curl -s -w "%{http_code}" -X GET "http://localhost:4006/api/auth/org-departments/$DEPT_ID" \
      -H "Authorization: Bearer $TOKEN")

    GET_DEPT_HTTP_CODE="${GET_DEPT_RESPONSE: -3}"
    GET_DEPT_BODY="${GET_DEPT_RESPONSE%???}"

    echo "HTTP Status: $GET_DEPT_HTTP_CODE"

    if [ "$GET_DEPT_HTTP_CODE" = "200" ]; then
        echo "✅ Get department by ID is working!"
        echo "Department details:"
        echo "$GET_DEPT_BODY" | jq '.department | {id, name, organizationId, organization}' 2>/dev/null || echo "$GET_DEPT_BODY"
    else
        echo "❌ Get department by ID failed. HTTP $GET_DEPT_HTTP_CODE"
        echo "Response: $GET_DEPT_BODY"
    fi

    echo ""
    echo "9. Testing Update Department..."
    UPDATE_DEPT_RESPONSE=$(curl -s -w "%{http_code}" -X PUT "http://localhost:4006/api/auth/org-departments/$DEPT_ID" \
      -H "Authorization: Bearer $TOKEN" \
      -H "Content-Type: application/json" \
      -d '{
        "name": "Updated Engineering",
        "description": "Updated engineering department description"
      }')

    UPDATE_DEPT_HTTP_CODE="${UPDATE_DEPT_RESPONSE: -3}"
    UPDATE_DEPT_BODY="${UPDATE_DEPT_RESPONSE%???}"

    echo "HTTP Status: $UPDATE_DEPT_HTTP_CODE"

    if [ "$UPDATE_DEPT_HTTP_CODE" = "200" ]; then
        echo "✅ Update department is working!"
        echo "Updated department:"
        echo "$UPDATE_DEPT_BODY" | jq '.department | {id, name, description}' 2>/dev/null || echo "$UPDATE_DEPT_BODY"
    else
        echo "❌ Update department failed. HTTP $UPDATE_DEPT_HTTP_CODE"
        echo "Response: $UPDATE_DEPT_BODY"
    fi
fi

echo ""
echo "10. Testing Employee Creation with Organization Department..."
UNIQUE_EMAIL="deptemployee$(date +%s)@applygoal.com"

CREATE_EMPLOYEE_RESPONSE=$(curl -s -w "%{http_code}" -X POST "http://localhost:4006/api/auth/employees" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"email\": \"$UNIQUE_EMAIL\",
    \"password\": \"Employee123!\",
    \"firstName\": \"Dept\",
    \"lastName\": \"Employee\",
    \"departmentName\": \"Updated Engineering\",
    \"organizationName\": \"ApplyGoal\",
    \"jobType\": \"Full-time\",
    \"jobStatus\": \"Active\"
  }")

CREATE_EMPLOYEE_HTTP_CODE="${CREATE_EMPLOYEE_RESPONSE: -3}"
CREATE_EMPLOYEE_BODY="${CREATE_EMPLOYEE_RESPONSE%???}"

echo "HTTP Status: $CREATE_EMPLOYEE_HTTP_CODE"

if [ "$CREATE_EMPLOYEE_HTTP_CODE" = "200" ] || [ "$CREATE_EMPLOYEE_HTTP_CODE" = "201" ]; then
    echo "✅ Employee creation with organization department is working!"
    echo "Employee created with department: Updated Engineering"
else
    echo "❌ Employee creation with organization department failed. HTTP $CREATE_EMPLOYEE_HTTP_CODE"
    echo "Response: $CREATE_EMPLOYEE_BODY"
fi

echo ""
echo "📋 Summary:"
echo "Organization Department Creation: $([ "$CREATE_DEPT_HTTP_CODE" = "200" ] || [ "$CREATE_DEPT_HTTP_CODE" = "201" ] && echo "✅ WORKING" || echo "❌ FAILED ($CREATE_DEPT_HTTP_CODE)")"
echo "Get Organization Departments: $([ "$GET_ORG_DEPTS_HTTP_CODE" = "200" ] && echo "✅ WORKING" || echo "❌ FAILED ($GET_ORG_DEPTS_HTTP_CODE)")"
echo "List Organization Departments: $([ "$LIST_DEPTS_HTTP_CODE" = "200" ] && echo "✅ WORKING" || echo "❌ FAILED ($LIST_DEPTS_HTTP_CODE)")"
if [ "$DEPT_ID" != "null" ] && [ -n "$DEPT_ID" ]; then
    echo "Get Department by ID: $([ "$GET_DEPT_HTTP_CODE" = "200" ] && echo "✅ WORKING" || echo "❌ FAILED ($GET_DEPT_HTTP_CODE)")"
    echo "Update Department: $([ "$UPDATE_DEPT_HTTP_CODE" = "200" ] && echo "✅ WORKING" || echo "❌ FAILED ($UPDATE_DEPT_HTTP_CODE)")"
fi
echo "Employee with Org Department: $([ "$CREATE_EMPLOYEE_HTTP_CODE" = "200" ] || [ "$CREATE_EMPLOYEE_HTTP_CODE" = "201" ] && echo "✅ WORKING" || echo "❌ FAILED ($CREATE_EMPLOYEE_HTTP_CODE)")"

echo ""
echo "🎯 Test Data Used:"
echo "Organization ID: $ORG_ID"
echo "Department ID: $DEPT_ID"
echo "Employee Email: $UNIQUE_EMAIL"

echo ""
echo "🔍 Debug commands if issues persist:"
echo "docker compose logs -f auth-service | grep -i department"
echo "docker compose logs -f auth-apigw | grep -i department"

echo ""
echo "Manual test commands:"
echo "# List organization departments"
echo "curl -X GET 'http://localhost:4006/api/auth/organizations/$ORG_ID/departments' -H 'Authorization: Bearer \$TOKEN'"
echo ""
echo "# Create organization department"
echo "curl -X POST 'http://localhost:4006/api/auth/organizations/$ORG_ID/departments' \\"
echo "  -H 'Authorization: Bearer \$TOKEN' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"name\": \"Custom Department\", \"description\": \"Custom department for testing\"}'"
