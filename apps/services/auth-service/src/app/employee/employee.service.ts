import {
  Injectable,
  NotFoundException,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op, UniqueConstraintError } from 'sequelize';
import { firstValueFrom } from 'rxjs';
import { EmployeePersonal } from './models/employee-personal.model';
import { EmployeeAddress } from './models/employee-address.model';
import { EmployeeEmergencyContact } from './models/employee-emergency-contact.model';
import { EmployeeIdentityDoc } from './models/employee-identity-doc.model';
import { EmployeeBankAccount } from './models/employee-bank-account.model';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { UpdateEmployeeDto } from './dto/update-employee.dto';
import { AuditClientService } from '../audit/audit.service';
import { AppService } from '../app.service';
import {
  CreateEmployeeRequest,
  UpdateEmployeeRequest,
  GetEmployeeRequest,
  ListEmployeesRequest,
  RemoveEmployeeRequest,
  EmployeeResponse,
  ListEmployeesResponse,
  RemoveEmployeeResponse,
  EmployeeInfo,
  CreateEmployeeData,
  UpdateEmployeeData,
  EmployeeAddressInfo,
  EmployeeEmergencyContactInfo,
  EmployeeIdentityDocInfo,
  EmployeeBankAccountInfo,
  JOB_TYPES,
  JOB_STATUSES,
  BLOOD_GROUPS,
  GENDERS,
} from './employee.interface';
import { CreateAuditLogRequest } from '../auth/auth.interfaces';

@Injectable()
export class EmployeeService {
  private readonly logger = new Logger(EmployeeService.name);

  constructor(
    @InjectModel(EmployeePersonal)
    private readonly employeeModel: typeof EmployeePersonal,
    @InjectModel(EmployeeAddress)
    private readonly employeeAddressModel: typeof EmployeeAddress,
    @InjectModel(EmployeeEmergencyContact)
    private readonly employeeEmergencyContactModel: typeof EmployeeEmergencyContact,
    @InjectModel(EmployeeIdentityDoc)
    private readonly employeeIdentityDocModel: typeof EmployeeIdentityDoc,
    @InjectModel(EmployeeBankAccount)
    private readonly employeeBankAccountModel: typeof EmployeeBankAccount,
    private readonly auditService: AuditClientService,
    private readonly appService: AppService
  ) {}

  // Audit-log helper, mirroring AuthService
  private async createAuditLog(payload: CreateAuditLogRequest) {
    const start = Date.now();
    try {
      await firstValueFrom(this.auditService.createAuditLog(payload));
      this.appService.trackAuditLogRequest(
        'create',
        'success',
        (Date.now() - start) / 1000
      );
    } catch (error) {
      this.appService.trackAuditLogRequest(
        'create',
        'error',
        (Date.now() - start) / 1000
      );
      this.logger.error('Failed to create audit log', error);
    }
  }

  // Metrics tracking helper
  private trackMetrics(
    operation: string,
    status: 'success' | 'failure' | 'error',
    duration: number
  ) {
    try {
      this.appService.trackAuthorization('employee', operation, status);
    } catch (err) {
      this.logger.warn(`Metrics error: ${err.message}`);
    }
  }

  async createEmployee(
    request: CreateEmployeeRequest
  ): Promise<EmployeeResponse> {
    const startTime = Date.now();
    try {
      this.logger.log(`Creating employee for user: ${request.userId}`);

      if (request.jobType && !JOB_TYPES.includes(request.jobType as any)) {
        throw new BadRequestException(
          `Invalid job type. Must be one of: ${JOB_TYPES.join(', ')}`
        );
      }
      if (
        request.jobStatus &&
        !JOB_STATUSES.includes(request.jobStatus as any)
      ) {
        throw new BadRequestException(
          `Invalid job status. Must be one of: ${JOB_STATUSES.join(', ')}`
        );
      }

      const employeeData: CreateEmployeeData = {
        userId: request.userId,
        lastName: request.lastName,
        firstName: request.firstName,
        joiningDate: request.joiningDate
          ? new Date(request.joiningDate)
          : undefined,
        jobType: request.jobType,
        jobStatus: request.jobStatus,
        dateOfBirth: request.dateOfBirth
          ? new Date(request.dateOfBirth)
          : undefined,
        bloodGroup: request.bloodGroup,
        gender: request.gender,
        agencyId: request.agencyId,
        orgId: request.orgId,
      };

      const employee = await this.employeeModel.create(employeeData as any);

      if (request.addresses?.length) {
        await this.createEmployeeAddresses(
          BigInt(employee.userId),
          request.addresses
        );
      }
      if (request.emergencyContacts?.length) {
        await this.createEmployeeEmergencyContacts(
          BigInt(employee.userId),
          request.emergencyContacts
        );
      }
      if (request.identityDocs?.length) {
        await this.createEmployeeIdentityDocs(
          BigInt(employee.userId),
          request.identityDocs
        );
      }
      if (request.bankAccounts?.length) {
        await this.createEmployeeBankAccounts(
          BigInt(employee.userId),
          request.bankAccounts
        );
      }

      // Audit-log
      await this.createAuditLog({
        userId: Number(request.requestUserId),
        userRole: request.roleName,
        actions: 'CREATE_EMPLOYEE',
        serviceName: 'auth-service',
        resourceType: 'Employee',
        resourceId: Number(employee.userId),
        description: `Created employee for user: ${employee.userId}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });

      this.logger.log(`Employee created successfully: ${employee.userId}`);

      // Fetch the complete employee with all relations
      const fullEmployee = await this.employeeModel.findByPk(employee.userId, {
        include: { all: true, nested: true },
      });

      if (!fullEmployee) {
        this.logger.error(
          `Failed to fetch created employee: ${employee.userId}`
        );
        throw new Error('Employee created but could not be retrieved');
      }

      // Metrics
      this.trackMetrics('create_employee', 'success', Date.now() - startTime);

      return {
        success: true,
        message: 'Employee created successfully',
        employee: await this.mapToEmployeeInfo(fullEmployee),
      };
    } catch (error) {
      this.logger.error(
        `Error creating employee: ${error.message}`,
        error.stack
      );
      this.trackMetrics('create_employee', 'error', Date.now() - startTime);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new Error('Failed to create employee');
    }
  }

  private async createEmployeeAddresses(
    userId: bigint,
    addresses: EmployeeAddressInfo[]
  ): Promise<void> {
    const addressData = addresses.map((addr) => ({
      userId: Number(userId),
      addressType: addr.addressType,
      addressLine: addr.addressLine,
      country: addr.country,
      state: addr.state,
      city: addr.city,
      postalCode: addr.postalCode,
    }));
    await this.employeeAddressModel.bulkCreate(addressData);
  }

  private async createEmployeeEmergencyContacts(
    userId: bigint,
    contacts: EmployeeEmergencyContactInfo[]
  ): Promise<void> {
    const contactData = contacts.map((contact) => ({
      userId: Number(userId),
      category: contact.category,
      name: contact.name,
      relationship: contact.relationship,
      address: contact.address,
      phoneNumber: contact.phoneNumber,
      email: contact.email,
    }));
    await this.employeeEmergencyContactModel.bulkCreate(contactData);
  }

  private async createEmployeeIdentityDocs(
    userId: bigint,
    docs: EmployeeIdentityDocInfo[]
  ): Promise<void> {
    const docData = docs.map((doc) => ({
      userId: Number(userId),
      docType: doc.docType,
      nationality: doc.nationality,
      issueDate: doc.issueDate ? new Date(doc.issueDate) : undefined,
      expiryDate: doc.expiryDate ? new Date(doc.expiryDate) : undefined,
    }));
    await this.employeeIdentityDocModel.bulkCreate(docData);
  }

  private async createEmployeeBankAccounts(
    userId: bigint,
    accounts: EmployeeBankAccountInfo[]
  ): Promise<void> {
    const accountData = accounts.map((account) => ({
      userId: Number(userId),
      accountHolder: account.accountHolder,
      accountNumber: account.accountNumber,
      bankName: account.bankName,
      branchName: account.branchName,
    }));
    await this.employeeBankAccountModel.bulkCreate(accountData);
  }

  async mapToEmployeeInfo(employee: EmployeePersonal): Promise<EmployeeInfo> {
    // If employee already has relations loaded, use it directly
    let fullEmployee = employee;

    // If relations are not loaded, fetch them
    if (
      !employee.addresses &&
      !employee.emergencyContacts &&
      !employee.identityDocs &&
      !employee.bankAccounts
    ) {
      const fetchedEmployee = await this.employeeModel.findByPk(
        employee.userId,
        {
          include: { all: true, nested: true },
        }
      );
      if (!fetchedEmployee) {
        throw new NotFoundException(
          `Employee not found with userId: ${employee.userId}`
        );
      }
      fullEmployee = fetchedEmployee;
    }
    return {
      userId: BigInt(fullEmployee.userId),
      lastName: fullEmployee.lastName,
      firstName: fullEmployee.firstName,
      joiningDate: fullEmployee.joiningDate?.toISOString(),
      jobType: fullEmployee.jobType,
      jobStatus: fullEmployee.jobStatus,
      dateOfBirth: fullEmployee.dateOfBirth?.toISOString(),
      bloodGroup: fullEmployee.bloodGroup,
      gender: fullEmployee.gender,
      agencyId: fullEmployee.agencyId
        ? BigInt(fullEmployee.agencyId)
        : undefined,
      orgId: fullEmployee.orgId,
      addresses: fullEmployee.addresses?.map((addr) => ({
        id: BigInt(addr.id),
        userId: BigInt(addr.userId),
        addressType: addr.addressType,
        addressLine: addr.addressLine,
        country: addr.country,
        state: addr.state,
        city: addr.city,
        postalCode: addr.postalCode,
      })),
      emergencyContacts: fullEmployee.emergencyContacts?.map((contact) => ({
        id: BigInt(contact.id),
        userId: BigInt(contact.userId),
        category: contact.category,
        name: contact.name,
        relationship: contact.relationship,
        address: contact.address,
        phoneNumber: contact.phoneNumber,
        email: contact.email,
      })),
      identityDocs: fullEmployee.identityDocs?.map((doc) => ({
        id: BigInt(doc.id),
        userId: BigInt(doc.userId),
        docType: doc.docType,
        nationality: doc.nationality,
        issueDate: doc.issueDate?.toISOString(),
        expiryDate: doc.expiryDate?.toISOString(),
      })),
      bankAccounts: fullEmployee.bankAccounts?.map((account) => ({
        id: BigInt(account.id),
        userId: BigInt(account.userId),
        accountHolder: account.accountHolder,
        accountNumber: account.accountNumber,
        bankName: account.bankName,
        branchName: account.branchName,
      })),
      createdAt: fullEmployee.createdAt,
      updatedAt: fullEmployee.updatedAt,
    };
  }

  // Legacy methods remain unchanged
  async create(data: CreateEmployeeDto): Promise<EmployeePersonal> {
    return this.employeeModel.create(data as any);
  }

  async findAll(): Promise<EmployeePersonal[]> {
    return this.employeeModel.findAll({
      include: { all: true, nested: true },
    });
  }

  async findOne(id: number): Promise<EmployeePersonal> {
    const emp = await this.employeeModel.findByPk(id, {
      include: { all: true, nested: true },
    });
    if (!emp) throw new NotFoundException(`Employee ${id} not found`);
    return emp;
  }

  async update(id: number, data: UpdateEmployeeDto): Promise<EmployeePersonal> {
    const emp = await this.findOne(id);
    await emp.update(data as any);
    return emp;
  }

  async remove(id: number): Promise<{ deleted: boolean }> {
    const emp = await this.findOne(id);
    await emp.destroy();
    return { deleted: true };
  }
}
