import {
  Body,
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Headers,
  UnauthorizedException,
  Param,
  Query,
  HttpException,
  HttpStatus,
  Logger,
  Req,
  Ip,
} from '@nestjs/common';
import { AuthClientService } from './auth.service';
import { firstValueFrom } from 'rxjs';
import {
  AssignDepartmentRequest,
  BulkCreateModulesResponse,
  CreateDepartmentRequest,
  CreateModuleRequest,
  CreateModuleResponse,
  CreateRoleWithDetailsRequest,
  ModuleInput,
  NewDepartmentInfo,
  CreateEmployeeRequest,
  UpdateEmployeeRequest,
  GetEmployeeRequest,
  ListEmployeesRequest,
  RemoveEmployeeRequest,
} from './auth.interface';
import {
  Permissions,
  Departments,
  Public,
  RequiresSubFeatures,
  Roles,
  CurrentUser,
} from '@apply-goal-backend/auth';
import { toNumber } from './utils/serializeResponse';
import { UserPermissions } from '@apply-goal-backend/common';

@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(private readonly authService: AuthClientService) {}

  // ---------- Authentication --------------
  // #region
  @Public()
  @Post('register')
  async register(
    @Body()
    registerData: {
      name: string;
      email: string;
      nationality: string;
      organizationName: string;
      password: string;
      phone: string;
      roleName: string;
      departmentName: string;
      ipAddress?: string;
      userAgent?: string;
    }
  ) {
    try {
      this.logger.log(
        `Registration attempt for user: ${registerData.roleName}`
      );
      return await firstValueFrom(this.authService.register(registerData));
    } catch (error) {
      this.logger.error(
        `Registration failed for user: ${registerData.email}`,
        error.stack
      );
      throw new HttpException('Registration failed', HttpStatus.BAD_REQUEST);
    }
  }

  @Public()
  @Post('login')
  async login(
    @Body()
    loginData: {
      email: string;
      password: string;
      ipAddress: string;
      userAgent?: string;
    }
  ) {
    try {
      this.logger.log(`Login attempt for user: ${loginData.email}`);
      return await this.authService.login(
        loginData.email,
        loginData.password,
        loginData.ipAddress,
        loginData.userAgent
      );
    } catch (error) {
      this.logger.error(
        `Login failed for user: ${loginData.email}`,
        error.stack
      );
      throw new UnauthorizedException('Invalid credentials');
    }
  }

  @Public()
  @Post('logout')
  async logout(@Headers('authorization') authHeader: string) {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedException('Invalid token format');
    }

    const token = authHeader.split(' ')[1];
    try {
      this.logger.log('Logout request received');
      return await firstValueFrom(this.authService.logout(token));
    } catch (error) {
      this.logger.error('Logout failed', error.stack);
      throw new HttpException(
        'Logout failed',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Public()
  @Get('validate')
  async validateToken(@Headers('authorization') authHeader: string) {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedException('Invalid token format');
    }

    const token = authHeader.split(' ')[1];
    try {
      this.logger.log('Token validation request received');
      return await this.authService.validateToken(token);
    } catch (error) {
      this.logger.error('Token validation failed', error.stack);
      throw new UnauthorizedException('Invalid token');
    }
  }

  @Public()
  @Post('refresh')
  async refreshToken(@Body() refreshData: { refreshToken: string }) {
    try {
      this.logger.log('Token refresh request received');
      return await firstValueFrom(
        this.authService.refreshToken(refreshData.refreshToken)
      );
    } catch (error) {
      this.logger.error('Token refresh failed', error.stack);
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  @Public()
  @Post('sso')
  async ssoLogin(
    @Body()
    ssoData: {
      provider: 'google'; // extend for others later
      token: string;
      email: string;
      name: string;
      ipAddress?: string;
      userAgent?: string;
    }
  ) {
    try {
      this.logger.log(`SSO login attempt using ${ssoData.provider} provider`);
      const response = this.authService.ssoAuth({
        provider: ssoData.provider,
        token: ssoData.token,
        email: ssoData.email,
        name: ssoData.name,
        ipAddress: ssoData.ipAddress,
        userAgent: ssoData.userAgent,
      });
      return response;
    } catch (error) {
      this.logger.error(
        `SSO login failed for provider ${ssoData.provider}`,
        error.stack
      );
      throw new HttpException(
        'SSO Authentication failed',
        HttpStatus.UNAUTHORIZED
      );
    }
  }

  @Public()
  @Post('verify-otp')
  async verifyOTP(
    @Body()
    otpData: {
      email: string;
      otp: string;
      type: string;
      ipAddress?: string;
      userAgent?: string;
    }
  ) {
    try {
      this.logger.log(`OTP validation attempt for user: ${otpData.email}`);
      return await firstValueFrom(
        this.authService.verifyOtp(
          otpData.email,
          otpData.otp,
          otpData.type,
          otpData.ipAddress,
          otpData.userAgent
        )
      );
    } catch (error) {
      this.logger.error(
        `OTP validation failed for user: ${otpData.email}`,
        error.stack
      );
      throw new HttpException('OTP validation failed', HttpStatus.BAD_REQUEST);
    }
  }

  @Public()
  @Post('generate-otp')
  async generateOTP(
    @Body()
    otpData: {
      email: string;
      type: string;
      ipAddress?: string;
      userAgent?: string;
    }
  ) {
    try {
      this.logger.log(`OTP generation request for user: ${otpData.email}`);
      return await firstValueFrom(
        this.authService.generateOtp(
          otpData.email,
          otpData.type,
          otpData.ipAddress,
          otpData.userAgent
        )
      );
    } catch (error) {
      this.logger.error(
        `OTP generation failed for user: ${otpData.email}`,
        error.stack
      );
      throw new HttpException('OTP generation failed', HttpStatus.BAD_REQUEST);
    }
  }
  // #endregion

  // ---------- User management endpoints ------------------
  // #region

  // #endregion

  // ----------- Role ----------
  // #region
  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Post('role')
  async createRoleWithDetails(
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body() roleData: CreateRoleWithDetailsRequest
  ) {
    try {
      return await firstValueFrom(
        this.authService.createRoleWithDetails({
          ...roleData,
          getArgs: {
            userId,
            roleName: roles[0],
            ipAddress,
            userAgent,
          },
        })
      );
    } catch (err) {
      this.logger.error('createRoleWithDetails failed', err.stack);
      throw new HttpException(
        'Failed to create role',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Get('role-details/:roleName')
  async getRoleDetails(
    @Param('roleName') roleName: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      return await firstValueFrom(
        this.authService.getRoleDetails(roleName, {
          userId,
          roleName: roles[0],
          ipAddress,
          userAgent,
        })
      );
    } catch (err) {
      this.logger.error('getRoleDetails failed', err.stack);
      throw new HttpException(
        'Failed to get role details',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Get('roles')
  async getRolesWithDetails(
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log('Get roles and permissions with users request');
      return await firstValueFrom(
        this.authService.getRolesWithDetails({
          userId,
          roleName: roles[0],
          ipAddress,
          userAgent,
        })
      );
    } catch (error) {
      this.logger.error(
        'Get roles and permissions with users failed',
        error.stack
      );
      throw new HttpException(
        'Failed to get roles and permissions with users',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Put('role/:id/:name')
  async renameRole(
    @Param('id') id: number,
    @Param('name') name: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log(`Update role request: ${name}`);
      return await firstValueFrom(
        this.authService.renameRole({
          id,
          roleName: name,
          getArgs: {
            userId,
            roleName: roles[0],
            ipAddress,
            userAgent,
          },
        })
      );
    } catch (error) {
      this.logger.error(`Update role failed for: ${name}`, error.stack);
      throw new HttpException('Failed to update role', HttpStatus.BAD_REQUEST);
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Delete('role/:id')
  async deleteRole(
    @Param('id') id: number,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log(`Delete role request: ${id}`);
      return await firstValueFrom(
        this.authService.deleteRole({
          id,
          getArgs: {
            userId,
            roleName: roles[0],
            ipAddress,
            userAgent,
          },
        })
      );
    } catch (error) {
      this.logger.error(`Delete role failed for: ${id}`, error.stack);
      throw new HttpException('Failed to delete role', HttpStatus.BAD_REQUEST);
    }
  }

  // #endregion

  // ─── Module management endpoints ────────────────────────────────────────
  // #region
  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Post('modules')
  async createModule(
    @Body() moduleData: CreateModuleRequest
  ): Promise<CreateModuleResponse> {
    try {
      this.logger.log(`Create module request: ${moduleData.name}`);
      return await firstValueFrom(this.authService.createModule(moduleData));
    } catch (error) {
      this.logger.error(
        `Create module failed for: ${moduleData.name}`,
        error.stack
      );
      throw new HttpException(
        'Failed to create module',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Post('modules/bulk')
  async bulkCreateModules(
    @CurrentUser('sub') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body() modulesData: ModuleInput[]
  ): Promise<BulkCreateModulesResponse> {
    try {
      return await firstValueFrom(
        this.authService.bulkCreateModules({
          modules: modulesData,
          userId,
          roleName: roles[0],
          ipAddress,
          userAgent,
        })
      );
    } catch (err) {
      this.logger.error('bulkCreateModules failed', err.stack);
      throw new HttpException(
        'Failed to create modules',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Get('modules')
  async listModules(
    @CurrentUser('sub') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log('List modules request');
      const resp = await firstValueFrom(
        this.authService.listModules({
          userId,
          roleName: roles[0],
          ipAddress,
          userAgent,
        })
      );
      resp.modules = resp.modules.map((m: any) => ({
        ...m,
        id: toNumber(m.id),
        features: m.features?.map((f: any) => ({
          ...f,
          id: toNumber(f.id),
          subFeatures: f.subFeatures?.map((sf: any) => ({
            ...sf,
            id: toNumber(sf.id),
          })),
        })),
      }));
      return resp;
    } catch (error) {
      this.logger.error('List modules failed', error.stack);
      throw new HttpException(
        'Failed to list modules',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
  // #endregion

  // ─── Department management endpoints ───────────────────────────────────
  // #region
  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Post('departments')
  async createDepartment(
    @CurrentUser('id') userId: string,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body() deptData: NewDepartmentInfo[],
    @Req() req: Request
  ) {
    try {
      this.logger.log(
        `Create department request: ${JSON.stringify(deptData, null, 2)}`
      );
      return await firstValueFrom(
        this.authService.createDepartment({
          departments: deptData,
          userId: Number(userId),
          roleName: roles[0],
          ipAddress,
          userAgent,
        })
      );
    } catch (error) {
      this.logger.error(
        `Create department failed for: ${JSON.stringify(deptData, null, 2)}`,
        error.stack
      );
      throw new HttpException(
        'Failed to create department',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Post('departments/assign')
  async assignDepartment(@Body() assignData: AssignDepartmentRequest) {
    try {
      this.logger.log(
        `Assign department ${assignData.departmentId} to user ${assignData.userId}`
      );
      return await firstValueFrom(
        this.authService.assignDepartment(assignData)
      );
    } catch (error) {
      this.logger.error(
        `Failed to assign department ${assignData.departmentId} to user ${assignData.userId}`,
        error.stack
      );
      throw new HttpException(
        'Failed to assign department',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Get('departments')
  async listDepartments(
    @CurrentUser('id') userId: string,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log('List departments request');
      return await firstValueFrom(
        this.authService.listDepartments({
          userId: Number(userId),
          roleName: roles[0],
          ipAddress,
          userAgent,
        })
      );
    } catch (error) {
      this.logger.error('List departments failed', error.stack);
      throw new HttpException(
        'Failed to list departments',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Get('departments-with-users')
  async listDepartmentsWithUsers(
    @CurrentUser('id') userId: string,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log('List departments with users request');
      return await firstValueFrom(
        this.authService.listDepartmentsWithUsers({
          userId: Number(userId),
          roleName: roles[0],
          ipAddress,
          userAgent,
        })
      );
    } catch (error) {
      this.logger.error('List departments with users failed', error.stack);
      throw new HttpException(
        'Failed to list departments with users',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
  // #endregion

  // ─── Employee Management endpoints ─────────────────────────────────────
  // #region
  @Permissions(UserPermissions.SS_EMPLOYEE_MANAGEMENT)
  @Post('employees')
  async createEmployee(
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body()
    employeeData: Omit<
      CreateEmployeeRequest,
      'requestUserId' | 'roleName' | 'ipAddress' | 'userAgent'
    >
  ) {
    try {
      this.logger.log(
        `Create employee request for email: ${employeeData.email}`
      );

      // Validate required fields
      if (!employeeData.email) {
        throw new HttpException(
          'Email is required for employee creation',
          HttpStatus.BAD_REQUEST
        );
      }

      return await firstValueFrom(
        this.authService.createEmployee({
          ...employeeData,
          requestUserId: userId,
          roleName: roles[0],
          ipAddress,
          userAgent,
        })
      );
    } catch (error) {
      this.logger.error(
        `Create employee failed for email: ${employeeData.email}`,
        error.stack
      );

      // Handle specific error cases
      if (error.message?.includes('Email already exists')) {
        throw new HttpException('Email already exists', HttpStatus.CONFLICT);
      }

      if (
        error.message?.includes('Role') &&
        error.message?.includes('not found')
      ) {
        throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
      }

      throw new HttpException(
        'Failed to create employee',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Permissions(UserPermissions.SS_EMPLOYEE_MANAGEMENT)
  @Get('employees/:id')
  async getEmployee(
    @Param('id') id: number,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log(`Get employee request: ${id}`);
      return await firstValueFrom(
        this.authService.getEmployee({
          id,
          requestUserId: userId,
          roleName: roles[0],
          ipAddress,
          userAgent,
        })
      );
    } catch (error) {
      this.logger.error(`Get employee failed for: ${id}`, error.stack);
      throw new HttpException('Failed to get employee', HttpStatus.BAD_REQUEST);
    }
  }

  @Permissions(UserPermissions.SS_EMPLOYEE_MANAGEMENT)
  @Get('employees')
  async listEmployees(
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
    @Query('jobType') jobType?: string,
    @Query('jobStatus') jobStatus?: string,
    @Query('agencyId') agencyId?: number,
    @Query('orgId') orgId?: string
  ) {
    try {
      this.logger.log('List employees request');
      return await firstValueFrom(
        this.authService.listEmployees({
          page,
          limit,
          search,
          jobType,
          jobStatus,
          agencyId,
          orgId,
          requestUserId: userId,
          roleName: roles[0],
          ipAddress,
          userAgent,
        })
      );
    } catch (error) {
      this.logger.error('List employees failed', error.stack);
      throw new HttpException(
        'Failed to list employees',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Permissions(UserPermissions.SS_EMPLOYEE_MANAGEMENT)
  @Put('employees/:id')
  async updateEmployee(
    @Param('id') id: number,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body()
    employeeData: Omit<
      UpdateEmployeeRequest,
      'id' | 'requestUserId' | 'roleName' | 'ipAddress' | 'userAgent'
    >
  ) {
    try {
      this.logger.log(`Update employee request: ${id}`);
      return await firstValueFrom(
        this.authService.updateEmployee({
          id,
          ...employeeData,
          requestUserId: userId,
          roleName: roles[0],
          ipAddress,
          userAgent,
        })
      );
    } catch (error) {
      this.logger.error(`Update employee failed for: ${id}`, error.stack);
      throw new HttpException(
        'Failed to update employee',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Permissions(UserPermissions.SS_EMPLOYEE_MANAGEMENT)
  @Delete('employees/:id')
  async removeEmployee(
    @Param('id') id: number,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log(`Remove employee request: ${id}`);
      return await firstValueFrom(
        this.authService.removeEmployee({
          id,
          requestUserId: userId,
          roleName: roles[0],
          ipAddress,
          userAgent,
        })
      );
    } catch (error) {
      this.logger.error(`Remove employee failed for: ${id}`, error.stack);
      throw new HttpException(
        'Failed to remove employee',
        HttpStatus.BAD_REQUEST
      );
    }
  }
  // #endregion
}
