#!/bin/bash

echo "🔧 Fixing Employee gRPC Methods..."

echo "1. Stopping auth-service to apply EmployeeController registration..."
docker compose stop auth-service

echo "2. Removing auth-service container to ensure clean restart..."
docker compose rm -f auth-service

echo "3. Starting auth-service with <PERSON>ployeeController registered..."
docker compose up -d auth-service

echo "4. Waiting for auth-service to be healthy..."
sleep 15

echo "5. Testing auth-service health..."
for i in {1..10}; do
    if curl -f -s http://localhost:5003/health > /dev/null; then
        echo "✅ Auth service is healthy"
        break
    else
        echo "⏳ Waiting for auth-service... (attempt $i/10)"
        sleep 3
    fi
done

echo "6. Getting fresh Super Admin token..."
TOKEN=$(curl -s -X POST "http://localhost:4006/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SuperAdmin@123",
    "ipAddress": "127.0.0.1"
  }' | jq -r '.accessToken')

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
    echo "❌ Failed to get token. Check login credentials."
    exit 1
fi

echo "✅ Got fresh token: ${TOKEN:0:50}..."

echo "7. Testing employee API endpoints..."

echo ""
echo "🧪 Testing ListEmployees..."
LIST_RESPONSE=$(curl -s -w "%{http_code}" -X GET "http://localhost:4006/api/auth/employees" \
  -H "Authorization: Bearer $TOKEN" \
  -H "User-Agent: curl/7.68.0")

LIST_HTTP_CODE="${LIST_RESPONSE: -3}"
LIST_BODY="${LIST_RESPONSE%???}"

echo "HTTP Status: $LIST_HTTP_CODE"

if [ "$LIST_HTTP_CODE" = "200" ]; then
    echo "✅ ListEmployees is working!"
    echo "Response: $LIST_BODY" | jq . 2>/dev/null || echo "$LIST_BODY"
else
    echo "❌ ListEmployees failed. HTTP $LIST_HTTP_CODE"
    echo "Response: $LIST_BODY"
fi

echo ""
echo "🧪 Testing CreateEmployee..."
CREATE_RESPONSE=$(curl -s -w "%{http_code}" -X POST "http://localhost:4006/api/auth/employees" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -H "User-Agent: curl/7.68.0" \
  -d '{
    "userId": 999,
    "firstName": "Test",
    "lastName": "Employee",
    "jobType": "Full-time",
    "jobStatus": "Active",
    "dateOfBirth": "1990-01-01",
    "bloodGroup": "O+",
    "gender": "Male"
  }')

CREATE_HTTP_CODE="${CREATE_RESPONSE: -3}"
CREATE_BODY="${CREATE_RESPONSE%???}"

echo "HTTP Status: $CREATE_HTTP_CODE"

if [ "$CREATE_HTTP_CODE" = "200" ] || [ "$CREATE_HTTP_CODE" = "201" ]; then
    echo "✅ CreateEmployee is working!"
    echo "Response: $CREATE_BODY" | jq . 2>/dev/null || echo "$CREATE_BODY"
else
    echo "❌ CreateEmployee failed. HTTP $CREATE_HTTP_CODE"
    echo "Response: $CREATE_BODY"
fi

echo ""
echo "🧪 Testing GetEmployee (ID: 1 - Super Admin)..."
GET_RESPONSE=$(curl -s -w "%{http_code}" -X GET "http://localhost:4006/api/auth/employees/1" \
  -H "Authorization: Bearer $TOKEN" \
  -H "User-Agent: curl/7.68.0")

GET_HTTP_CODE="${GET_RESPONSE: -3}"
GET_BODY="${GET_RESPONSE%???}"

echo "HTTP Status: $GET_HTTP_CODE"

if [ "$GET_HTTP_CODE" = "200" ]; then
    echo "✅ GetEmployee is working!"
    echo "Response: $GET_BODY" | jq . 2>/dev/null || echo "$GET_BODY"
else
    echo "❌ GetEmployee failed. HTTP $GET_HTTP_CODE"
    echo "Response: $GET_BODY"
fi

echo ""
echo "📋 Summary:"
if [ "$LIST_HTTP_CODE" = "200" ]; then
    echo "✅ ListEmployees: WORKING"
else
    echo "❌ ListEmployees: FAILED ($LIST_HTTP_CODE)"
fi

if [ "$CREATE_HTTP_CODE" = "200" ] || [ "$CREATE_HTTP_CODE" = "201" ]; then
    echo "✅ CreateEmployee: WORKING"
else
    echo "❌ CreateEmployee: FAILED ($CREATE_HTTP_CODE)"
fi

if [ "$GET_HTTP_CODE" = "200" ]; then
    echo "✅ GetEmployee: WORKING"
else
    echo "❌ GetEmployee: FAILED ($GET_HTTP_CODE)"
fi

echo ""
echo "🎯 Your fresh token for manual testing:"
echo "export TOKEN='$TOKEN'"
echo ""
echo "Manual test commands:"
echo "curl -X GET 'http://localhost:4006/api/auth/employees' -H 'Authorization: Bearer \$TOKEN'"
echo "curl -X POST 'http://localhost:4006/api/auth/employees' -H 'Authorization: Bearer \$TOKEN' -H 'Content-Type: application/json' -d '{\"userId\": 123, \"firstName\": \"Manual\", \"lastName\": \"Test\"}'"

echo ""
echo "🔍 If issues persist, check logs:"
echo "docker compose logs -f auth-service | grep -i employee"
echo "docker compose logs -f auth-apigw | grep -i employee"
