#!/bin/bash

echo "🔧 Testing Employee Creation with User Creation..."

echo "1. Restarting auth-service to apply the new employee creation logic..."
docker compose stop auth-service
docker compose rm -f auth-service
docker compose up -d auth-service

echo "2. Waiting for auth-service to be healthy..."
sleep 15

for i in {1..10}; do
    if curl -f -s http://localhost:5003/health > /dev/null; then
        echo "✅ Auth service is healthy"
        break
    else
        echo "⏳ Waiting for auth-service... (attempt $i/10)"
        sleep 3
    fi
done

echo "3. Getting fresh Super Admin token..."
TOKEN=$(curl -s -X POST "http://localhost:4006/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SuperAdmin@123",
    "ipAddress": "127.0.0.1"
  }' | jq -r '.accessToken')

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
    echo "❌ Failed to get token. Check login credentials."
    exit 1
fi

echo "✅ Got fresh token: ${TOKEN:0:50}..."

echo ""
echo "4. Testing Employee Creation with New User..."

# Generate unique email to avoid conflicts
UNIQUE_EMAIL="employee$(date +%s)@applygoal.com"

CREATE_RESPONSE=$(curl -s -w "%{http_code}" -X POST "http://localhost:4006/api/auth/employees" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -H "User-Agent: curl/7.68.0" \
  -d "{
    \"email\": \"$UNIQUE_EMAIL\",
    \"password\": \"Employee123!\",
    \"name\": \"Test Employee\",
    \"phone\": \"******-0123\",
    \"departmentName\": \"HR\",
    \"organizationName\": \"ApplyGoal\",
    \"employeeRoleName\": \"Employee\",
    \"firstName\": \"Test\",
    \"lastName\": \"Employee\",
    \"jobType\": \"Full-time\",
    \"jobStatus\": \"Active\",
    \"dateOfBirth\": \"1990-01-01\",
    \"bloodGroup\": \"O+\",
    \"gender\": \"Male\",
    \"addresses\": [
      {
        \"addressType\": \"Home\",
        \"addressLine\": \"123 Test Street\",
        \"country\": \"Canada\",
        \"state\": \"Ontario\",
        \"city\": \"Toronto\",
        \"postalCode\": \"M5V 3A8\"
      }
    ],
    \"emergencyContacts\": [
      {
        \"contactType\": \"Emergency\",
        \"contactName\": \"Emergency Contact\",
        \"contactPhone\": \"******-0124\",
        \"contactEmail\": \"<EMAIL>\",
        \"relationship\": \"Friend\"
      }
    ]
  }")

CREATE_HTTP_CODE="${CREATE_RESPONSE: -3}"
CREATE_BODY="${CREATE_RESPONSE%???}"

echo "HTTP Status: $CREATE_HTTP_CODE"

if [ "$CREATE_HTTP_CODE" = "200" ] || [ "$CREATE_HTTP_CODE" = "201" ]; then
    echo "✅ Employee Creation with User is working!"
    echo "Response:"
    echo "$CREATE_BODY" | jq . 2>/dev/null || echo "$CREATE_BODY"
    
    # Extract userId from response for further testing
    USER_ID=$(echo "$CREATE_BODY" | jq -r '.employee.userId' 2>/dev/null)
    
    echo ""
    echo "5. Testing Duplicate Email Prevention..."
    DUPLICATE_RESPONSE=$(curl -s -w "%{http_code}" -X POST "http://localhost:4006/api/auth/employees" \
      -H "Authorization: Bearer $TOKEN" \
      -H "Content-Type: application/json" \
      -H "User-Agent: curl/7.68.0" \
      -d "{
        \"email\": \"$UNIQUE_EMAIL\",
        \"firstName\": \"Duplicate\",
        \"lastName\": \"Test\"
      }")

    DUPLICATE_HTTP_CODE="${DUPLICATE_RESPONSE: -3}"
    DUPLICATE_BODY="${DUPLICATE_RESPONSE%???}"

    if [ "$DUPLICATE_HTTP_CODE" = "200" ] && echo "$DUPLICATE_BODY" | grep -q "Email already exists"; then
        echo "✅ Duplicate email prevention is working!"
        echo "Response: $DUPLICATE_BODY"
    else
        echo "❌ Duplicate email prevention failed. HTTP $DUPLICATE_HTTP_CODE"
        echo "Response: $DUPLICATE_BODY"
    fi
    
    echo ""
    echo "6. Testing Employee Login with Created User..."
    if [ "$USER_ID" != "null" ] && [ -n "$USER_ID" ]; then
        LOGIN_RESPONSE=$(curl -s -w "%{http_code}" -X POST "http://localhost:4006/api/auth/login" \
          -H "Content-Type: application/json" \
          -d "{
            \"email\": \"$UNIQUE_EMAIL\",
            \"password\": \"Employee123!\",
            \"ipAddress\": \"127.0.0.1\"
          }")

        LOGIN_HTTP_CODE="${LOGIN_RESPONSE: -3}"
        LOGIN_BODY="${LOGIN_RESPONSE%???}"

        if [ "$LOGIN_HTTP_CODE" = "200" ]; then
            echo "✅ Employee can login with created credentials!"
            EMPLOYEE_TOKEN=$(echo "$LOGIN_BODY" | jq -r '.accessToken' 2>/dev/null)
            echo "Employee token: ${EMPLOYEE_TOKEN:0:50}..."
        else
            echo "❌ Employee login failed. HTTP $LOGIN_HTTP_CODE"
            echo "Response: $LOGIN_BODY"
        fi
    fi

else
    echo "❌ Employee Creation with User failed. HTTP $CREATE_HTTP_CODE"
    echo "Response: $CREATE_BODY"
    
    echo ""
    echo "🔍 Checking auth-service logs for errors..."
    docker compose logs --tail=30 auth-service | grep -i "error\|exception\|employee"
fi

echo ""
echo "📋 Summary:"
echo "Employee Creation: $([ "$CREATE_HTTP_CODE" = "200" ] || [ "$CREATE_HTTP_CODE" = "201" ] && echo "✅ WORKING" || echo "❌ FAILED ($CREATE_HTTP_CODE)")"
echo "Duplicate Prevention: $([ "$DUPLICATE_HTTP_CODE" = "200" ] && echo "$DUPLICATE_BODY" | grep -q "Email already exists" && echo "✅ WORKING" || echo "❌ NEEDS CHECK")"

echo ""
echo "🎯 Test Data Used:"
echo "Email: $UNIQUE_EMAIL"
echo "Password: Employee123!"
echo "User ID: $USER_ID"

echo ""
echo "🔍 Debug commands if issues persist:"
echo "docker compose logs -f auth-service | grep -i employee"
echo "docker compose logs -f auth-apigw | grep -i employee"

echo ""
echo "Manual test command for creating employee:"
echo "curl -X POST 'http://localhost:4006/api/auth/employees' \\"
echo "  -H 'Authorization: Bearer \$TOKEN' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{"
echo "    \"email\": \"<EMAIL>\","
echo "    \"password\": \"NewEmployee123!\","
echo "    \"firstName\": \"New\","
echo "    \"lastName\": \"Employee\","
echo "    \"jobType\": \"Full-time\","
echo "    \"jobStatus\": \"Active\""
echo "  }'"
