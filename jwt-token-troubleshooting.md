# JWT Token Authentication Troubleshooting Guide

## Issue Found ✅
**Root Cause**: Missing `JWT_SECRET` environment variable in both auth-service and auth-apigw containers.

## Solution Applied ✅
Added `JWT_SECRET=applygoal-super-secret-jwt-key-2024` to both services in docker-compose.yml:

1. **auth-service** (line ~825)
2. **auth-apigw** (line ~672)

## Steps to Fix

### 1. Restart the Services
```bash
# Stop the affected services
docker compose stop auth-service auth-apigw

# Remove the containers to ensure fresh environment
docker compose rm -f auth-service auth-apigw

# Rebuild and start the services
docker compose up -d auth-service auth-apigw

# Check logs to ensure they start properly
docker compose logs -f auth-service
docker compose logs -f auth-apigw
```

### 2. Verify Services are Running
```bash
# Check service health
docker compose ps

# Test auth-service health
curl http://localhost:5003/health

# Test auth-apigw health  
curl http://localhost:4006/health
```

### 3. Get a Fresh Token
```bash
# Login to get a new token with the correct JWT secret
curl -X POST "http://localhost:4006/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SuperAdmin@123",
    "ipAddress": "127.0.0.1",
    "userAgent": "curl/7.68.0"
  }'
```

### 4. Test Token Validation
```bash
# Replace YOUR_NEW_JWT_TOKEN with the token from step 3
curl -X GET "http://localhost:4006/api/auth/validate" \
  -H "Authorization: Bearer YOUR_NEW_JWT_TOKEN"
```

### 5. Test Employee Endpoints
```bash
# Test employee listing with the new token
curl -X GET "http://localhost:4006/api/auth/employees" \
  -H "Authorization: Bearer YOUR_NEW_JWT_TOKEN" \
  -H "User-Agent: curl/7.68.0"

# Test employee creation
curl -X POST "http://localhost:4006/api/auth/employees" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_NEW_JWT_TOKEN" \
  -H "User-Agent: curl/7.68.0" \
  -d '{
    "userId": 123,
    "firstName": "Test",
    "lastName": "Employee",
    "jobType": "Full-time",
    "jobStatus": "Active"
  }'
```

## Additional Troubleshooting

### Check JWT Secret Consistency
Both services should now use the same JWT secret. Verify in logs:

```bash
# Check if services are using the correct JWT secret
docker compose logs auth-service | grep -i jwt
docker compose logs auth-apigw | grep -i jwt
```

### Verify Database Connection
```bash
# Check if auth-service can connect to database
docker compose logs auth-service | grep -i "database\|postgres\|connection"
```

### Check gRPC Communication
```bash
# Verify auth-apigw can communicate with auth-service
docker compose logs auth-apigw | grep -i "grpc\|auth-service"
```

### Token Debugging
If you still get token errors, check:

1. **Token Format**: Ensure it starts with "Bearer "
2. **Token Expiry**: Tokens expire in 1 hour by default
3. **Database Token Record**: Check if token exists in database

```sql
-- Check token in database
SELECT * FROM tokens WHERE accessToken = 'YOUR_TOKEN_HERE';
```

## Expected Results

After applying the fix:

✅ **Login should work** and return a valid JWT token
✅ **Token validation** should return user information
✅ **Employee endpoints** should be accessible with Super Admin token
✅ **No more "Invalid or expired token" errors**

## Security Note

In production, use a strong, randomly generated JWT secret:
```bash
# Generate a secure JWT secret
openssl rand -base64 64
```

## Common Issues After Fix

1. **Old tokens still invalid**: Get a fresh token after restart
2. **Services not restarted**: Ensure you stopped and restarted both services
3. **Environment not loaded**: Check docker compose logs for environment variable loading
4. **Database connection issues**: Verify postgres is healthy

## Verification Commands

```bash
# Quick verification script
echo "Testing auth-service health..."
curl -s http://localhost:5003/health | jq .

echo "Testing auth-apigw health..."
curl -s http://localhost:4006/health | jq .

echo "Testing login..."
curl -s -X POST "http://localhost:4006/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>", 
    "password": "SuperAdmin@123",
    "ipAddress": "127.0.0.1"
  }' | jq .
```

The JWT secret mismatch was the root cause of your authentication issues. After restarting the services with the proper JWT_SECRET environment variable, your Super Admin token should work correctly with all employee endpoints.
